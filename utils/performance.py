"""性能优化和监控工具"""

import asyncio
import time
import psutil
import gc
from typing import Dict, List, Any, Optional, Callable, TypeVar, Awaitable
from datetime import datetime, timedelta
from collections import deque, defaultdict
from functools import wraps
from dataclasses import dataclass, field
import threading
import weakref

T = TypeVar('T')


@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    memory_before: Optional[float] = None
    memory_after: Optional[float] = None
    memory_delta: Optional[float] = None
    cpu_percent: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    
    def finish(self, success: bool = True, error_message: Optional[str] = None) -> None:
        """完成性能测量"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.success = success
        self.error_message = error_message
        
        # 测量内存使用
        if self.memory_before is not None:
            process = psutil.Process()
            self.memory_after = process.memory_info().rss / 1024 / 1024  # MB
            self.memory_delta = self.memory_after - self.memory_before


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history: deque = deque(maxlen=max_history)
        self.operation_stats: Dict[str, List[float]] = defaultdict(list)
        self._lock = threading.Lock()
    
    def start_measurement(self, operation_name: str) -> PerformanceMetrics:
        """开始性能测量"""
        process = psutil.Process()
        
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            start_time=time.time(),
            memory_before=process.memory_info().rss / 1024 / 1024,  # MB
            cpu_percent=process.cpu_percent()
        )
        
        return metrics
    
    def record_metrics(self, metrics: PerformanceMetrics) -> None:
        """记录性能指标"""
        with self._lock:
            self.metrics_history.append(metrics)
            
            if metrics.duration is not None:
                self.operation_stats[metrics.operation_name].append(metrics.duration)
                
                # 保持统计数据在合理范围内
                if len(self.operation_stats[metrics.operation_name]) > 100:
                    self.operation_stats[metrics.operation_name] = \
                        self.operation_stats[metrics.operation_name][-100:]
    
    def get_operation_stats(self, operation_name: str) -> Dict[str, float]:
        """获取操作统计信息"""
        durations = self.operation_stats.get(operation_name, [])
        
        if not durations:
            return {}
        
        return {
            'count': len(durations),
            'avg_duration': sum(durations) / len(durations),
            'min_duration': min(durations),
            'max_duration': max(durations),
            'total_duration': sum(durations)
        }
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        process = psutil.Process()
        
        return {
            'memory_usage_mb': process.memory_info().rss / 1024 / 1024,
            'cpu_percent': process.cpu_percent(),
            'open_files': len(process.open_files()),
            'threads': process.num_threads(),
            'connections': len(process.connections()),
            'uptime_seconds': time.time() - process.create_time()
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        recent_metrics = [
            m for m in self.metrics_history
            if m.end_time and m.end_time > time.time() - 3600  # 最近1小时
        ]
        
        failed_operations = [m for m in recent_metrics if not m.success]
        
        return {
            'total_operations': len(self.metrics_history),
            'recent_operations': len(recent_metrics),
            'failed_operations': len(failed_operations),
            'operation_stats': {
                name: self.get_operation_stats(name)
                for name in self.operation_stats.keys()
            },
            'system_stats': self.get_system_stats()
        }


class AsyncBatchProcessor:
    """异步批处理器"""
    
    def __init__(
        self, 
        batch_size: int = 10, 
        max_concurrent: int = 5,
        delay_between_batches: float = 0.1
    ):
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.delay_between_batches = delay_between_batches
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_items(
        self, 
        items: List[T], 
        processor: Callable[[List[T]], Awaitable[List[Any]]]
    ) -> List[Any]:
        """批量处理项目"""
        results = []
        
        # 分批处理
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            
            async with self.semaphore:
                try:
                    batch_results = await processor(batch)
                    results.extend(batch_results)
                except Exception as e:
                    # 记录错误但继续处理
                    print(f"批处理错误: {e}")
                    continue
            
            # 批次间延迟
            if i + self.batch_size < len(items):
                await asyncio.sleep(self.delay_between_batches)
        
        return results


class MemoryManager:
    """内存管理器"""
    
    def __init__(self, max_memory_mb: int = 500):
        self.max_memory_mb = max_memory_mb
        self.object_cache: Dict[str, Any] = {}
        self.cache_access_times: Dict[str, float] = {}
        self.weak_refs: weakref.WeakValueDictionary = weakref.WeakValueDictionary()
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def check_memory_pressure(self) -> bool:
        """检查内存压力"""
        return self.get_memory_usage() > self.max_memory_mb
    
    def cleanup_if_needed(self) -> None:
        """如果需要则清理内存"""
        if self.check_memory_pressure():
            self.force_cleanup()
    
    def force_cleanup(self) -> None:
        """强制清理内存"""
        # 清理缓存
        self.clear_old_cache_entries()
        
        # 强制垃圾回收
        gc.collect()
        
        print(f"内存清理完成，当前使用: {self.get_memory_usage():.2f} MB")
    
    def clear_old_cache_entries(self, max_age_seconds: int = 3600) -> None:
        """清理旧的缓存条目"""
        current_time = time.time()
        expired_keys = [
            key for key, access_time in self.cache_access_times.items()
            if current_time - access_time > max_age_seconds
        ]
        
        for key in expired_keys:
            self.object_cache.pop(key, None)
            self.cache_access_times.pop(key, None)
    
    def cache_object(self, key: str, obj: Any, use_weak_ref: bool = False) -> None:
        """缓存对象"""
        if use_weak_ref:
            try:
                self.weak_refs[key] = obj
            except TypeError:
                # 对象不支持弱引用，使用普通缓存
                self.object_cache[key] = obj
        else:
            self.object_cache[key] = obj
        
        self.cache_access_times[key] = time.time()
        
        # 检查内存压力
        self.cleanup_if_needed()
    
    def get_cached_object(self, key: str) -> Optional[Any]:
        """获取缓存对象"""
        # 先检查弱引用缓存
        obj = self.weak_refs.get(key)
        if obj is not None:
            self.cache_access_times[key] = time.time()
            return obj
        
        # 再检查普通缓存
        obj = self.object_cache.get(key)
        if obj is not None:
            self.cache_access_times[key] = time.time()
            return obj
        
        return None


def performance_monitor(operation_name: str):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            monitor = performance_monitor_instance
            metrics = monitor.start_measurement(operation_name)
            
            try:
                result = await func(*args, **kwargs)
                metrics.finish(success=True)
                return result
            except Exception as e:
                metrics.finish(success=False, error_message=str(e))
                raise
            finally:
                monitor.record_metrics(metrics)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            monitor = performance_monitor_instance
            metrics = monitor.start_measurement(operation_name)
            
            try:
                result = func(*args, **kwargs)
                metrics.finish(success=True)
                return result
            except Exception as e:
                metrics.finish(success=False, error_message=str(e))
                raise
            finally:
                monitor.record_metrics(metrics)
        
        # 检查函数是否为协程
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class ConnectionPool:
    """连接池管理器"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self.available_connections: asyncio.Queue = asyncio.Queue(maxsize=max_connections)
        self.active_connections: int = 0
        self._lock = asyncio.Lock()
    
    async def get_connection(self):
        """获取连接"""
        try:
            # 尝试从池中获取现有连接
            connection = self.available_connections.get_nowait()
            return connection
        except asyncio.QueueEmpty:
            # 如果没有可用连接且未达到最大连接数，创建新连接
            async with self._lock:
                if self.active_connections < self.max_connections:
                    self.active_connections += 1
                    return self._create_connection()
                else:
                    # 等待可用连接
                    return await self.available_connections.get()
    
    async def return_connection(self, connection):
        """归还连接"""
        if connection and not connection.closed:
            await self.available_connections.put(connection)
        else:
            # 连接已关闭，减少活跃连接计数
            async with self._lock:
                self.active_connections -= 1
    
    def _create_connection(self):
        """创建新连接（子类实现）"""
        raise NotImplementedError("子类必须实现 _create_connection 方法")


# 全局实例
performance_monitor_instance = PerformanceMonitor()
memory_manager = MemoryManager()
batch_processor = AsyncBatchProcessor()
