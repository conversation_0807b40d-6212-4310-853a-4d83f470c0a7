"""安全工具和防护措施"""

import hashlib
import hmac
import secrets
import time
import re
from typing import Dict, List, Optional, Set, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque
from pathlib import Path


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int = 100, time_window: int = 3600):
        """
        初始化速率限制器
        
        Args:
            max_requests: 时间窗口内最大请求数
            time_window: 时间窗口（秒）
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests: Dict[str, deque] = defaultdict(deque)
    
    def is_allowed(self, identifier: str) -> bool:
        """检查是否允许请求"""
        now = time.time()
        window_start = now - self.time_window
        
        # 清理过期请求
        request_times = self.requests[identifier]
        while request_times and request_times[0] < window_start:
            request_times.popleft()
        
        # 检查是否超过限制
        if len(request_times) >= self.max_requests:
            return False
        
        # 记录新请求
        request_times.append(now)
        return True
    
    def get_reset_time(self, identifier: str) -> Optional[datetime]:
        """获取限制重置时间"""
        request_times = self.requests[identifier]
        if not request_times:
            return None
        
        oldest_request = request_times[0]
        reset_time = oldest_request + self.time_window
        return datetime.fromtimestamp(reset_time)


class TokenSanitizer:
    """Token 清理和脱敏工具"""
    
    # 常见的敏感 Token 模式
    SENSITIVE_PATTERNS = {
        'github_pat': r'ghp_[A-Za-z0-9]{36}',
        'github_oauth': r'gho_[A-Za-z0-9]{36}',
        'github_app': r'ghs_[A-Za-z0-9]{36}',
        'github_refresh': r'ghr_[A-Za-z0-9]{76}',
        'aws_access_key': r'AKIA[0-9A-Z]{16}',
        'aws_secret_key': r'[A-Za-z0-9/+=]{40}',
        'slack_token': r'xox[bpoa]-[0-9]{12}-[0-9]{12}-[a-zA-Z0-9]{24}',
        'discord_token': r'[MN][A-Za-z\d]{23}\.[\w-]{6}\.[\w-]{27}',
        'telegram_token': r'[0-9]{8,10}:[a-zA-Z0-9_-]{35}',
        'jwt_token': r'eyJ[A-Za-z0-9_-]*\.eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*',
    }
    
    @classmethod
    def mask_token(cls, token: str, show_chars: int = 4) -> str:
        """掩码 Token，只显示前后几个字符"""
        if len(token) <= show_chars * 2:
            return '*' * len(token)
        
        return f"{token[:show_chars]}{'*' * (len(token) - show_chars * 2)}{token[-show_chars:]}"
    
    @classmethod
    def hash_token(cls, token: str, salt: Optional[str] = None) -> str:
        """生成 Token 的安全哈希"""
        if salt is None:
            salt = secrets.token_hex(16)
        
        combined = f"{salt}{token}".encode('utf-8')
        return hashlib.sha256(combined).hexdigest()
    
    @classmethod
    def sanitize_text(cls, text: str) -> str:
        """清理文本中的敏感信息"""
        sanitized = text
        
        for token_type, pattern in cls.SENSITIVE_PATTERNS.items():
            def replace_match(match):
                token = match.group(0)
                return cls.mask_token(token)
            
            sanitized = re.sub(pattern, replace_match, sanitized)
        
        return sanitized
    
    @classmethod
    def detect_token_types(cls, text: str) -> List[Dict[str, str]]:
        """检测文本中的 Token 类型"""
        findings = []
        
        for token_type, pattern in cls.SENSITIVE_PATTERNS.items():
            matches = re.finditer(pattern, text)
            for match in matches:
                findings.append({
                    'type': token_type,
                    'token': match.group(0),
                    'start': match.start(),
                    'end': match.end(),
                    'masked': cls.mask_token(match.group(0))
                })
        
        return findings


class SecurityAuditor:
    """安全审计工具"""
    
    def __init__(self):
        self.audit_log: List[Dict[str, Any]] = []
        self.suspicious_activities: Set[str] = set()
    
    def log_security_event(
        self, 
        event_type: str, 
        details: Dict[str, Any], 
        severity: str = "info"
    ) -> None:
        """记录安全事件"""
        event = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'severity': severity,
            'details': details,
            'event_id': secrets.token_hex(8)
        }
        
        self.audit_log.append(event)
        
        # 检测可疑活动
        if severity in ['warning', 'error', 'critical']:
            self._detect_suspicious_activity(event)
    
    def _detect_suspicious_activity(self, event: Dict[str, Any]) -> None:
        """检测可疑活动模式"""
        event_type = event['event_type']
        
        # 检测频繁的失败尝试
        if event_type in ['token_validation_failed', 'proxy_connection_failed']:
            recent_failures = [
                e for e in self.audit_log[-10:]  # 最近10个事件
                if e['event_type'] == event_type and 
                   datetime.fromisoformat(e['timestamp']) > datetime.now() - timedelta(minutes=5)
            ]
            
            if len(recent_failures) >= 5:
                self.suspicious_activities.add(f"frequent_{event_type}")
                self.log_security_event(
                    'suspicious_activity_detected',
                    {
                        'pattern': f"frequent_{event_type}",
                        'count': len(recent_failures),
                        'time_window': '5_minutes'
                    },
                    severity='warning'
                )
    
    def get_security_summary(self) -> Dict[str, Any]:
        """获取安全摘要"""
        total_events = len(self.audit_log)
        severity_counts = defaultdict(int)
        
        for event in self.audit_log:
            severity_counts[event['severity']] += 1
        
        return {
            'total_events': total_events,
            'severity_breakdown': dict(severity_counts),
            'suspicious_activities': list(self.suspicious_activities),
            'last_24h_events': len([
                e for e in self.audit_log
                if datetime.fromisoformat(e['timestamp']) > datetime.now() - timedelta(days=1)
            ])
        }


class SecureFileHandler:
    """安全文件处理工具"""
    
    @staticmethod
    def secure_write(file_path: Path, content: str, mode: int = 0o600) -> None:
        """安全写入文件"""
        # 确保父目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 设置安全权限
        file_path.chmod(mode)
    
    @staticmethod
    def secure_read(file_path: Path) -> Optional[str]:
        """安全读取文件"""
        if not file_path.exists():
            return None
        
        # 检查文件权限
        file_mode = oct(file_path.stat().st_mode)[-3:]
        if file_mode not in ['600', '644', '400']:
            raise PermissionError(f"文件权限不安全: {file_mode}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    @staticmethod
    def validate_file_path(file_path: Path, allowed_dirs: List[Path]) -> bool:
        """验证文件路径是否在允许的目录内"""
        try:
            resolved_path = file_path.resolve()
            
            for allowed_dir in allowed_dirs:
                allowed_resolved = allowed_dir.resolve()
                if resolved_path.is_relative_to(allowed_resolved):
                    return True
            
            return False
        except (OSError, ValueError):
            return False


class InputValidator:
    """输入验证工具"""
    
    # 危险字符和模式
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',  # XSS
        r'javascript:',               # JavaScript 协议
        r'data:text/html',           # Data URI
        r'vbscript:',                # VBScript
        r'on\w+\s*=',                # 事件处理器
        r'expression\s*\(',          # CSS 表达式
        r'\$\{.*?\}',                # 模板注入
        r'<%.*?%>',                  # 服务器端脚本
    ]
    
    @classmethod
    def sanitize_input(cls, user_input: str) -> str:
        """清理用户输入"""
        if not isinstance(user_input, str):
            return str(user_input)
        
        sanitized = user_input
        
        # 移除危险模式
        for pattern in cls.DANGEROUS_PATTERNS:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        
        # 转义 HTML 特殊字符
        html_escape_table = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;',
            '/': '&#x2F;',
        }
        
        for char, escape in html_escape_table.items():
            sanitized = sanitized.replace(char, escape)
        
        return sanitized
    
    @classmethod
    def validate_github_repo(cls, repo_name: str) -> bool:
        """验证 GitHub 仓库名格式"""
        if not repo_name or not isinstance(repo_name, str):
            return False
        
        # GitHub 仓库名格式: owner/repo
        pattern = r'^[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+$'
        return bool(re.match(pattern, repo_name))
    
    @classmethod
    def validate_url_safety(cls, url: str) -> bool:
        """验证 URL 安全性"""
        if not url or not isinstance(url, str):
            return False
        
        # 只允许 HTTP/HTTPS
        if not url.startswith(('http://', 'https://')):
            return False
        
        # 检查危险模式
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, url, re.IGNORECASE):
                return False
        
        return True


# 全局实例
rate_limiter = RateLimiter()
security_auditor = SecurityAuditor()
token_sanitizer = TokenSanitizer()
input_validator = InputValidator()
