#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
"""

import logging
import logging.handlers
from pathlib import Path
from datetime import datetime
from typing import Optional
from config.settings import settings


class SecurityLogger:
    """安全日志记录器"""

    def __init__(self, name: str = "GitHubTokenHunter", config_override: Optional[dict] = None):
        self.name = name
        self.config = config_override or settings.log

        # 创建日志目录
        self.log_dir = Path(self.config.log_dir)
        self.log_dir.mkdir(exist_ok=True)

        # 设置日志器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, self.config.log_level.upper()))

        # 清除现有处理器
        self.logger.handlers.clear()

        # 添加处理器
        self._setup_file_handler()
        self._setup_console_handler()

    def _setup_file_handler(self) -> None:
        """设置文件处理器"""
        log_file = self.log_dir / f"token_hunter_{datetime.now().strftime('%Y%m%d')}.log"

        # 使用RotatingFileHandler支持日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=self.config.max_log_size,
            backupCount=self.config.backup_count,
            encoding='utf-8'
        )

        file_formatter = logging.Formatter(
            '%(asctime)s | %(name)s | %(levelname)s | %(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)

    def _setup_console_handler(self) -> None:
        """设置控制台处理器"""
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)

    def info(self, message: str, *args, **kwargs):
        self.logger.info(message, *args, **kwargs)

    def warning(self, message: str, *args, **kwargs):
        self.logger.warning(message, *args, **kwargs)

    def error(self, message: str, *args, **kwargs):
        self.logger.error(message, *args, **kwargs)

    def critical(self, message: str, *args, **kwargs):
        self.logger.critical(message, *args, **kwargs)

    def debug(self, message: str, *args, **kwargs):
        self.logger.debug(message, *args, **kwargs)

    def exception(self, message: str, *args, **kwargs):
        self.logger.exception(message, *args, **kwargs)


# 创建默认日志器实例
logger = SecurityLogger()

