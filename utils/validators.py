"""数据验证工具"""

import re
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pathlib import Path


class ValidationError(Exception):
    """验证错误"""
    pass


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_github_token(token: str) -> bool:
        """验证 GitHub Token 格式"""
        if not token or not isinstance(token, str):
            return False
        
        # GitHub Personal Access Token 格式
        patterns = [
            r'^ghp_[A-Za-z0-9]{36}$',  # 新格式
            r'^[a-f0-9]{40}$',         # 旧格式
        ]
        
        return any(re.match(pattern, token) for pattern in patterns)
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """验证 URL 格式"""
        url_pattern = re.compile(
            r'^https?://'  # http:// 或 https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # 域名
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP
            r'(?::\d+)?'  # 可选端口
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return bool(url_pattern.match(url))
    
    @staticmethod
    def validate_proxy_config(config: Dict[str, Any]) -> List[str]:
        """验证代理配置"""
        errors = []
        
        if not isinstance(config, dict):
            errors.append("代理配置必须是字典格式")
            return errors
        
        required_fields = ['host', 'port']
        for field in required_fields:
            if field not in config:
                errors.append(f"缺少必需字段: {field}")
        
        if 'port' in config:
            port = config['port']
            if not isinstance(port, int) or not (1 <= port <= 65535):
                errors.append("端口必须是 1-65535 之间的整数")
        
        if 'proxy_type' in config:
            valid_types = ['http', 'https', 'socks4', 'socks5']
            if config['proxy_type'] not in valid_types:
                errors.append(f"代理类型必须是: {', '.join(valid_types)}")
        
        return errors
    
    @staticmethod
    def validate_search_config(config: Dict[str, Any]) -> List[str]:
        """验证搜索配置"""
        errors = []
        
        numeric_fields = {
            'max_results_per_query': (1, 1000),
            'max_concurrent_searches': (1, 10),
            'search_delay': (0.1, 60.0)
        }
        
        for field, (min_val, max_val) in numeric_fields.items():
            if field in config:
                value = config[field]
                if not isinstance(value, (int, float)):
                    errors.append(f"{field} 必须是数字")
                elif not (min_val <= value <= max_val):
                    errors.append(f"{field} 必须在 {min_val}-{max_val} 范围内")
        
        return errors
    
    @staticmethod
    def validate_file_path(path: Union[str, Path]) -> bool:
        """验证文件路径"""
        try:
            path_obj = Path(path)
            # 检查路径是否安全（不包含危险字符）
            dangerous_patterns = ['..', '~', '$']
            path_str = str(path_obj)
            return not any(pattern in path_str for pattern in dangerous_patterns)
        except Exception:
            return False
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名，移除危险字符"""
        # 移除或替换危险字符
        dangerous_chars = '<>:"/\\|?*'
        for char in dangerous_chars:
            filename = filename.replace(char, '_')
        
        # 限制长度
        if len(filename) > 255:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            max_name_len = 255 - len(ext) - 1 if ext else 255
            filename = name[:max_name_len] + ('.' + ext if ext else '')
        
        return filename
    
    @staticmethod
    def validate_token_pattern(token: str, token_type: str) -> bool:
        """验证 Token 是否符合特定类型的模式"""
        patterns = {
            'github_pat': r'^ghp_[A-Za-z0-9]{36}$',
            'github_oauth': r'^gho_[A-Za-z0-9]{36}$',
            'github_app': r'^ghs_[A-Za-z0-9]{36}$',
            'github_refresh': r'^ghr_[A-Za-z0-9]{76}$',
            'aws_access_key': r'^AKIA[0-9A-Z]{16}$',
            'aws_secret_key': r'^[A-Za-z0-9/+=]{40}$',
            'slack_token': r'^xox[bpoa]-[0-9]{12}-[0-9]{12}-[a-zA-Z0-9]{24}$',
        }
        
        pattern = patterns.get(token_type)
        if not pattern:
            return False
        
        return bool(re.match(pattern, token))


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.data_validator = DataValidator()
    
    def validate_complete_config(self, config: Dict[str, Any]) -> Dict[str, List[str]]:
        """验证完整配置"""
        errors = {}
        
        # 验证 GitHub Token
        if 'github_token' in config:
            token = config['github_token']
            if not self.data_validator.validate_github_token(token):
                errors['github_token'] = ['无效的 GitHub Token 格式']
        else:
            errors['github_token'] = ['缺少 GitHub Token 配置']
        
        # 验证代理配置
        if 'proxy' in config and config['proxy'].get('enabled'):
            proxy_errors = self.data_validator.validate_proxy_config(config['proxy'])
            if proxy_errors:
                errors['proxy'] = proxy_errors
        
        # 验证搜索配置
        if 'search' in config:
            search_errors = self.data_validator.validate_search_config(config['search'])
            if search_errors:
                errors['search'] = search_errors
        
        return errors
