#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理健康检查器
"""

import asyncio
import aiohttp
import time
from typing import List
from core.models import ProxyInfo
from utils.logger import SecurityLogger


class ProxyChecker:
    """代理健康检查器"""

    def __init__(self, logger: SecurityLogger):
        self.logger = logger
        self.test_urls = [
            'https://httpbin.org/ip',
            'https://api.github.com/zen',
            'https://ifconfig.me/ip',
            'https://jsonip.com'
        ]

    async def check_proxy_health(self, proxy: ProxyInfo) -> bool:
        """检查单个代理的健康状态"""
        start_time = time.time()

        # 构建代理URL
        proxy_url = proxy.url

        timeout = aiohttp.ClientTimeout(total=10)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            for test_url in self.test_urls:
                try:
                    async with session.get(
                            test_url,
                            proxy=proxy_url,
                            headers={'User-Agent': 'ProxyHealthCheck/1.0'}
                    ) as response:

                        if response.status == 200:
                            proxy.response_time = time.time() - start_time
                            proxy.is_active = True
                            proxy.success_count += 1
                            proxy.last_check = time.time()

                            # 验证代理确实在工作（IP应该不同）
                            if test_url == 'https://httpbin.org/ip':
                                try:
                                    data = await response.json()
                                    proxy_ip = data.get('origin', '').split(',')[0].strip()
                                    self.logger.debug(f"✅ 代理 {proxy.host}:{proxy.port} 工作正常，IP: {proxy_ip}")
                                except:
                                    pass

                            return True

                except Exception as e:
                    self.logger.debug(f"❌ 代理检查失败 {proxy.host}:{proxy.port} -> {test_url}: {str(e)}")
                    continue

        # 所有测试都失败
        proxy.is_active = False
        proxy.failure_count += 1
        proxy.last_check = time.time()

        return False

    async def batch_check_proxies(self, proxies: List[ProxyInfo], max_concurrent: int = 10) -> Dict:
        """批量检查代理健康状态"""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def check_with_semaphore(proxy):
            async with semaphore:
                return await self.check_proxy_health(proxy)

        tasks = [check_with_semaphore(proxy) for proxy in proxies]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        success_count = sum(1 for r in results if r is True)

        return {
            'total_checked': len(proxies),
            'active_proxies': success_count,
            'success_rate': success_count / len(proxies) if proxies else 0
        }
