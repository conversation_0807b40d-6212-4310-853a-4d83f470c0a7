#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理管理器模块
"""

import asyncio
import time
import yaml
import json
import requests
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Optional, Set, Dict, Any
from itertools import cycle
import random
import aiohttp
from urllib.parse import urlparse

from core.models import ProxyInfo
from core.exceptions import ProxyError
from utils.logger import SecurityLogger
from utils.security import rate_limiter, security_auditor
from utils.performance import performance_monitor
from config.settings import settings
from config.secrets import env_config


class ProxyManager:
    """增强的代理管理器"""

    def __init__(self, logger: Optional[SecurityLogger] = None):
        self.logger = logger or SecurityLogger("ProxyManager")
        self.config = settings.proxy
        self.proxies: List[ProxyInfo] = []
        self.healthy_proxies: List[ProxyInfo] = []
        self.proxy_cycle = None
        self.current_proxy: Optional[ProxyInfo] = None
        self.banned_ips: Set[str] = set()
        self.last_health_check = datetime.now()
        self.last_rotation = datetime.now()
        self.request_count = 0
        self.proxy_stats: Dict[str, Dict[str, Any]] = {}
        self.selection_strategy = "weighted_random"  # random, round_robin, weighted_random, fastest

        if self.config.enabled:
            self.load_proxies()
            asyncio.create_task(self._start_background_tasks())

    def load_proxies(self) -> None:
        """加载代理列表"""
        proxy_file = Path(self.config.proxy_file)

        if not proxy_file.exists():
            self.logger.warning(f"⚠️ 代理文件不存在: {proxy_file}")
            self._create_sample_proxy_file(proxy_file)
            return

        try:
            with open(proxy_file, 'r', encoding='utf-8') as f:
                if proxy_file.suffix.lower() == '.yaml':
                    proxy_data = yaml.safe_load(f)
                else:
                    proxy_data = json.load(f)

            proxy_list = proxy_data.get('proxies', []) if isinstance(proxy_data, dict) else proxy_data

            for proxy_config in proxy_list:
                if isinstance(proxy_config, str):
                    proxy_info = self._parse_proxy_string(proxy_config)
                elif isinstance(proxy_config, dict):
                    proxy_info = ProxyInfo(**proxy_config)
                else:
                    continue

                self.proxies.append(proxy_info)

            if self.proxies:
                self.proxy_cycle = cycle(self.proxies)
                self.logger.info(f"📡 成功加载 {len(self.proxies)} 个代理")
            else:
                self.logger.warning("⚠️ 未找到有效的代理配置")

        except Exception as e:
            self.logger.error(f"❌ 加载代理文件失败: {str(e)}")

    def _parse_proxy_string(self, proxy_str: str) -> ProxyInfo:
        """解析代理字符串格式: [user:pass@]host:port"""
        if '@' in proxy_str:
            auth_part, host_part = proxy_str.rsplit('@', 1)
            username, password = auth_part.split(':', 1)
        else:
            username, password = None, None
            host_part = proxy_str

        host, port = host_part.split(':', 1)

        return ProxyInfo(
            host=host.strip(),
            port=int(port.strip()),
            username=username.strip() if username else None,
            password=password.strip() if password else None
        )

    def _create_sample_proxy_file(self, proxy_path: Path) -> None:
        """创建示例代理配置文件"""
        sample_config = {
            'proxies': [
                {
                    'host': '127.0.0.1',
                    'port': 8080,
                    'proxy_type': 'http',
                    'username': None,
                    'password': None
                },
                {
                    'host': '127.0.0.1',
                    'port': 1080,
                    'proxy_type': 'socks5',
                    'username': 'username',
                    'password': 'password'
                },
                # 可以添加更多免费代理示例
                {
                    'host': 'proxy.example.com',
                    'port': 3128,
                    'proxy_type': 'http'
                }
            ]
        }

        proxy_path.parent.mkdir(parents=True, exist_ok=True)
        with open(proxy_path, 'w', encoding='utf-8') as f:
            yaml.dump(sample_config, f, default_flow_style=False, allow_unicode=True)

        self.logger.info(f"📝 已创建示例代理配置文件: {proxy_path}")

    def get_next_proxy(self) -> Optional[ProxyInfo]:
        """获取下一个可用代理"""
        if not self.proxies or not self.config.enabled:
            return None

        # 检查是否需要健康检查
        if (datetime.now() - self.last_health_check).seconds > self.config.health_check_interval:
            asyncio.create_task(self.check_all_proxies_health())

        # 尝试获取活跃的代理
        for _ in range(len(self.proxies)):
            proxy = next(self.proxy_cycle)
            if proxy.is_active and f"{proxy.host}:{proxy.port}" not in self.banned_ips:
                proxy.last_used = datetime.now()
                self.current_proxy = proxy
                return proxy

        # 如果没有活跃代理，重新激活失败次数较少的代理
        self._reactivate_proxies()

        return self.proxies[0] if self.proxies else None

    def _reactivate_proxies(self) -> None:
        """重新激活一些失败的代理"""
        for proxy in self.proxies:
            if not proxy.is_active and proxy.failure_count < self.config.max_failures:
                proxy.is_active = True
                proxy.failure_count = 0
                self.logger.info(f"🔄 重新激活代理: {proxy.host}:{proxy.port}")

    def mark_proxy_failed(self, proxy: ProxyInfo, error: str = "") -> None:
        """标记代理失败"""
        proxy.failure_count += 1

        # 检查错误类型
        error_lower = error.lower()
        critical_errors = ['403', 'forbidden', 'banned', 'blocked', 'timeout']

        if any(err in error_lower for err in critical_errors):
            proxy.is_active = False
            self.banned_ips.add(f"{proxy.host}:{proxy.port}")
            self.logger.warning(f"🚫 代理被标记为禁用: {proxy.host}:{proxy.port} - {error}")
        elif proxy.failure_count >= self.config.max_failures:
            proxy.is_active = False
            self.logger.warning(f"⚠️ 代理失败次数过多，暂时禁用: {proxy.host}:{proxy.port}")

    def mark_proxy_success(self, proxy: ProxyInfo) -> None:
        """标记代理成功"""
        proxy.success_count += 1
        proxy.is_active = True
        # 减少失败计数
        if proxy.failure_count > 0:
            proxy.failure_count = max(0, proxy.failure_count - 1)

    async def check_all_proxies_health(self) -> None:
        """检查所有代理的健康状态"""
        self.logger.info("🔍 开始代理健康检查...")

        from proxy.checker import ProxyChecker
        checker = ProxyChecker(self.logger)

        tasks = [checker.check_proxy_health(proxy) for proxy in self.proxies]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        active_count = sum(1 for proxy in self.proxies if proxy.is_active)
        self.last_health_check = datetime.now()

        self.logger.info(f"📊 代理健康检查完成: {active_count}/{len(self.proxies)} 个可用")

    def get_proxy_dict(self, proxy: ProxyInfo) -> Dict[str, str]:
        """获取requests格式的代理字典"""
        return {
            'http': proxy.url,
            'https': proxy.url
        }

    def get_stats(self) -> Dict:
        """获取代理统计信息"""
        if not self.proxies:
            return {
                'total': 0,
                'active': 0,
                'banned': 0,
                'avg_success_rate': 0.0
    async def _start_background_tasks(self) -> None:
        """启动后台任务"""
        # 健康检查任务
        asyncio.create_task(self._health_check_loop())
        # 代理轮换任务
        asyncio.create_task(self._rotation_loop())

    async def _health_check_loop(self) -> None:
        """健康检查循环"""
        while True:
            try:
                await self.check_all_proxies_health()
                await asyncio.sleep(self.config.health_check_interval)
            except Exception as e:
                self.logger.error(f"健康检查循环错误: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟

    async def _rotation_loop(self) -> None:
        """代理轮换循环"""
        while True:
            try:
                if self._should_rotate_proxy():
                    await self._rotate_proxy()
                await asyncio.sleep(self.config.rotation_interval)
            except Exception as e:
                self.logger.error(f"代理轮换循环错误: {e}")
                await asyncio.sleep(30)

    def _should_rotate_proxy(self) -> bool:
        """判断是否应该轮换代理"""
        if not self.current_proxy:
            return True

        # 基于时间的轮换
        time_since_rotation = datetime.now() - self.last_rotation
        if time_since_rotation.total_seconds() > self.config.rotation_interval:
            return True

        # 基于请求数的轮换
        max_requests = getattr(self.config, 'max_requests_per_proxy', 100)
        if self.request_count >= max_requests:
            return True

        # 基于代理健康状态的轮换
        if not self.current_proxy.is_healthy:
            return True

        return False

    async def _rotate_proxy(self) -> None:
        """轮换代理"""
        old_proxy = self.current_proxy
        new_proxy = await self.get_proxy()

        if new_proxy and new_proxy != old_proxy:
            self.current_proxy = new_proxy
            self.last_rotation = datetime.now()
            self.request_count = 0

            self.logger.info(f"🔄 代理轮换: {old_proxy.host if old_proxy else 'None'} -> {new_proxy.host}")

            # 记录轮换事件
            security_auditor.log_security_event(
                'proxy_rotation',
                {
                    'old_proxy': old_proxy.host if old_proxy else None,
                    'new_proxy': new_proxy.host,
                    'reason': 'scheduled_rotation'
                }
            )

    async def get_proxy(self) -> Optional[ProxyInfo]:
        """获取可用代理（增强版）"""
        if not self.config.enabled or not self.healthy_proxies:
            return None

        # 更新健康代理列表
        await self._update_healthy_proxies()

        if not self.healthy_proxies:
            self.logger.warning("⚠️ 没有可用的健康代理")
            return None

        # 根据策略选择代理
        proxy = self._select_proxy_by_strategy()

        if proxy:
            self.request_count += 1
            self._update_proxy_stats(proxy, 'selected')

        return proxy

    def _select_proxy_by_strategy(self) -> Optional[ProxyInfo]:
        """根据策略选择代理"""
        if not self.healthy_proxies:
            return None

        if self.selection_strategy == "random":
            return random.choice(self.healthy_proxies)

        elif self.selection_strategy == "round_robin":
            if not self.proxy_cycle:
                self.proxy_cycle = cycle(self.healthy_proxies)
            return next(self.proxy_cycle)

        elif self.selection_strategy == "weighted_random":
            return self._weighted_random_selection()

        elif self.selection_strategy == "fastest":
            return self._select_fastest_proxy()

        else:
            return random.choice(self.healthy_proxies)

    def _weighted_random_selection(self) -> Optional[ProxyInfo]:
        """加权随机选择"""
        if not self.healthy_proxies:
            return None

        # 计算权重
        weights = []
        for proxy in self.healthy_proxies:
            base_weight = getattr(proxy, 'weight', 1)

            # 根据成功率调整权重
            success_rate = proxy.success_rate
            weight = base_weight * (0.5 + success_rate * 0.5)

            # 根据响应时间调整权重
            if proxy.response_time:
                # 响应时间越短权重越高
                time_factor = max(0.1, 1.0 - (proxy.response_time / 10.0))
                weight *= time_factor

            weights.append(weight)

        # 加权随机选择
        total_weight = sum(weights)
        if total_weight <= 0:
            return random.choice(self.healthy_proxies)

        r = random.uniform(0, total_weight)
        cumulative_weight = 0

        for i, weight in enumerate(weights):
            cumulative_weight += weight
            if r <= cumulative_weight:
                return self.healthy_proxies[i]

        return self.healthy_proxies[-1]

    def _select_fastest_proxy(self) -> Optional[ProxyInfo]:
        """选择最快的代理"""
        if not self.healthy_proxies:
            return None

        # 按响应时间排序，选择最快的
        sorted_proxies = sorted(
            self.healthy_proxies,
            key=lambda p: p.response_time or float('inf')
        )

        return sorted_proxies[0]

    async def _update_healthy_proxies(self) -> None:
        """更新健康代理列表"""
        self.healthy_proxies = [
            proxy for proxy in self.proxies
            if proxy.is_active and proxy.is_healthy
        ]

        # 如果没有健康代理，尝试重新激活一些代理
        if not self.healthy_proxies:
            await self._try_reactivate_proxies()

    async def _try_reactivate_proxies(self) -> None:
        """尝试重新激活代理"""
        inactive_proxies = [p for p in self.proxies if not p.is_active]

        for proxy in inactive_proxies[:3]:  # 最多尝试3个
            if await self._test_proxy_health(proxy):
                proxy.is_active = True
                proxy.failure_count = 0
                self.logger.info(f"✅ 代理重新激活: {proxy.host}:{proxy.port}")

    def _update_proxy_stats(self, proxy: ProxyInfo, event: str) -> None:
        """更新代理统计信息"""
        proxy_key = f"{proxy.host}:{proxy.port}"

        if proxy_key not in self.proxy_stats:
            self.proxy_stats[proxy_key] = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'last_used': None,
                'avg_response_time': 0.0
            }

        stats = self.proxy_stats[proxy_key]

        if event == 'selected':
            stats['total_requests'] += 1
            stats['last_used'] = datetime.now()
        elif event == 'success':
            stats['successful_requests'] += 1
        elif event == 'failure':
            stats['failed_requests'] += 1

    def get_proxy_statistics(self) -> Dict[str, Any]:
        """获取代理统计信息"""
        total_proxies = len(self.proxies)
        healthy_proxies = len(self.healthy_proxies)

        return {
            'total_proxies': total_proxies,
            'healthy_proxies': healthy_proxies,
            'health_rate': healthy_proxies / total_proxies if total_proxies > 0 else 0,
            'current_proxy': f"{self.current_proxy.host}:{self.current_proxy.port}" if self.current_proxy else None,
            'selection_strategy': self.selection_strategy,
            'request_count': self.request_count,
            'proxy_details': self.proxy_stats
        }
            }

        active_count = sum(1 for p in self.proxies if p.is_active)
        avg_success_rate = sum(p.success_rate for p in self.proxies) / len(self.proxies)

        return {
            'total': len(self.proxies),
            'active': active_count,
            'banned': len(self.banned_ips),
            'avg_success_rate': avg_success_rate,
            'current_proxy': f"{self.current_proxy.host}:{self.current_proxy.port}" if self.current_proxy else None
        }
