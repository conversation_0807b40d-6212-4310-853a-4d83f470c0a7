#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理管理器模块
"""

import asyncio
import time
import yaml
import json
import requests
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Optional, Set, Dict
from itertools import cycle

from core.models import ProxyInfo
from utils.logger import SecurityLogger
from config.settings import settings


class ProxyManager:
    """代理管理器"""

    def __init__(self, logger: Optional[SecurityLogger] = None):
        self.logger = logger or SecurityLogger("ProxyManager")
        self.config = settings.proxy
        self.proxies: List[ProxyInfo] = []
        self.proxy_cycle = None
        self.current_proxy: Optional[ProxyInfo] = None
        self.banned_ips: Set[str] = set()
        self.last_health_check = datetime.now()

        if self.config.enabled:
            self.load_proxies()

    def load_proxies(self) -> None:
        """加载代理列表"""
        proxy_file = Path(self.config.proxy_file)

        if not proxy_file.exists():
            self.logger.warning(f"⚠️ 代理文件不存在: {proxy_file}")
            self._create_sample_proxy_file(proxy_file)
            return

        try:
            with open(proxy_file, 'r', encoding='utf-8') as f:
                if proxy_file.suffix.lower() == '.yaml':
                    proxy_data = yaml.safe_load(f)
                else:
                    proxy_data = json.load(f)

            proxy_list = proxy_data.get('proxies', []) if isinstance(proxy_data, dict) else proxy_data

            for proxy_config in proxy_list:
                if isinstance(proxy_config, str):
                    proxy_info = self._parse_proxy_string(proxy_config)
                elif isinstance(proxy_config, dict):
                    proxy_info = ProxyInfo(**proxy_config)
                else:
                    continue

                self.proxies.append(proxy_info)

            if self.proxies:
                self.proxy_cycle = cycle(self.proxies)
                self.logger.info(f"📡 成功加载 {len(self.proxies)} 个代理")
            else:
                self.logger.warning("⚠️ 未找到有效的代理配置")

        except Exception as e:
            self.logger.error(f"❌ 加载代理文件失败: {str(e)}")

    def _parse_proxy_string(self, proxy_str: str) -> ProxyInfo:
        """解析代理字符串格式: [user:pass@]host:port"""
        if '@' in proxy_str:
            auth_part, host_part = proxy_str.rsplit('@', 1)
            username, password = auth_part.split(':', 1)
        else:
            username, password = None, None
            host_part = proxy_str

        host, port = host_part.split(':', 1)

        return ProxyInfo(
            host=host.strip(),
            port=int(port.strip()),
            username=username.strip() if username else None,
            password=password.strip() if password else None
        )

    def _create_sample_proxy_file(self, proxy_path: Path) -> None:
        """创建示例代理配置文件"""
        sample_config = {
            'proxies': [
                {
                    'host': '127.0.0.1',
                    'port': 8080,
                    'proxy_type': 'http',
                    'username': None,
                    'password': None
                },
                {
                    'host': '127.0.0.1',
                    'port': 1080,
                    'proxy_type': 'socks5',
                    'username': 'username',
                    'password': 'password'
                },
                # 可以添加更多免费代理示例
                {
                    'host': 'proxy.example.com',
                    'port': 3128,
                    'proxy_type': 'http'
                }
            ]
        }

        proxy_path.parent.mkdir(parents=True, exist_ok=True)
        with open(proxy_path, 'w', encoding='utf-8') as f:
            yaml.dump(sample_config, f, default_flow_style=False, allow_unicode=True)

        self.logger.info(f"📝 已创建示例代理配置文件: {proxy_path}")

    def get_next_proxy(self) -> Optional[ProxyInfo]:
        """获取下一个可用代理"""
        if not self.proxies or not self.config.enabled:
            return None

        # 检查是否需要健康检查
        if (datetime.now() - self.last_health_check).seconds > self.config.health_check_interval:
            asyncio.create_task(self.check_all_proxies_health())

        # 尝试获取活跃的代理
        for _ in range(len(self.proxies)):
            proxy = next(self.proxy_cycle)
            if proxy.is_active and f"{proxy.host}:{proxy.port}" not in self.banned_ips:
                proxy.last_used = datetime.now()
                self.current_proxy = proxy
                return proxy

        # 如果没有活跃代理，重新激活失败次数较少的代理
        self._reactivate_proxies()

        return self.proxies[0] if self.proxies else None

    def _reactivate_proxies(self) -> None:
        """重新激活一些失败的代理"""
        for proxy in self.proxies:
            if not proxy.is_active and proxy.failure_count < self.config.max_failures:
                proxy.is_active = True
                proxy.failure_count = 0
                self.logger.info(f"🔄 重新激活代理: {proxy.host}:{proxy.port}")

    def mark_proxy_failed(self, proxy: ProxyInfo, error: str = "") -> None:
        """标记代理失败"""
        proxy.failure_count += 1

        # 检查错误类型
        error_lower = error.lower()
        critical_errors = ['403', 'forbidden', 'banned', 'blocked', 'timeout']

        if any(err in error_lower for err in critical_errors):
            proxy.is_active = False
            self.banned_ips.add(f"{proxy.host}:{proxy.port}")
            self.logger.warning(f"🚫 代理被标记为禁用: {proxy.host}:{proxy.port} - {error}")
        elif proxy.failure_count >= self.config.max_failures:
            proxy.is_active = False
            self.logger.warning(f"⚠️ 代理失败次数过多，暂时禁用: {proxy.host}:{proxy.port}")

    def mark_proxy_success(self, proxy: ProxyInfo) -> None:
        """标记代理成功"""
        proxy.success_count += 1
        proxy.is_active = True
        # 减少失败计数
        if proxy.failure_count > 0:
            proxy.failure_count = max(0, proxy.failure_count - 1)

    async def check_all_proxies_health(self) -> None:
        """检查所有代理的健康状态"""
        self.logger.info("🔍 开始代理健康检查...")

        from proxy.checker import ProxyChecker
        checker = ProxyChecker(self.logger)

        tasks = [checker.check_proxy_health(proxy) for proxy in self.proxies]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        active_count = sum(1 for proxy in self.proxies if proxy.is_active)
        self.last_health_check = datetime.now()

        self.logger.info(f"📊 代理健康检查完成: {active_count}/{len(self.proxies)} 个可用")

    def get_proxy_dict(self, proxy: ProxyInfo) -> Dict[str, str]:
        """获取requests格式的代理字典"""
        return {
            'http': proxy.url,
            'https': proxy.url
        }

    def get_stats(self) -> Dict:
        """获取代理统计信息"""
        if not self.proxies:
            return {
                'total': 0,
                'active': 0,
                'banned': 0,
                'avg_success_rate': 0.0
            }

        active_count = sum(1 for p in self.proxies if p.is_active)
        avg_success_rate = sum(p.success_rate for p in self.proxies) / len(self.proxies)

        return {
            'total': len(self.proxies),
            'active': active_count,
            'banned': len(self.banned_ips),
            'avg_success_rate': avg_success_rate,
            'current_proxy': f"{self.current_proxy.host}:{self.current_proxy.port}" if self.current_proxy else None
        }
