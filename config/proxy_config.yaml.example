# GitHub Token 扫描工具 - 代理配置示例
# 复制此文件为 proxy_config.yaml 并填入实际的代理信息

# =============================================================================
# 代理服务器列表
# =============================================================================

proxies:
  # HTTP 代理示例
  - name: "http_proxy_1"
    host: "proxy1.example.com"
    port: 8080
    proxy_type: "http"
    username: "your_username"      # 可选，如果代理需要认证
    password: "your_password"      # 可选，如果代理需要认证
    weight: 10                     # 权重，数值越高使用频率越高
    region: "US"                   # 地区标识，用于统计和选择
    provider: "ProxyProvider1"     # 代理提供商
    max_concurrent: 3              # 最大并发连接数
    enabled: true                  # 是否启用此代理

  # HTTPS 代理示例
  - name: "https_proxy_1"
    host: "secure-proxy.example.com"
    port: 443
    proxy_type: "https"
    username: "user123"
    password: "pass456"
    weight: 8
    region: "EU"
    provider: "SecureProxy"
    max_concurrent: 2
    enabled: true

  # SOCKS5 代理示例（推荐用于高匿名性）
  - name: "socks5_proxy_1"
    host: "socks.example.com"
    port: 1080
    proxy_type: "socks5"
    username: "socks_user"
    password: "socks_pass"
    weight: 15
    region: "Asia"
    provider: "SocksProvider"
    max_concurrent: 5
    enabled: true

  # SOCKS4 代理示例
  - name: "socks4_proxy_1"
    host: "old-socks.example.com"
    port: 1080
    proxy_type: "socks4"
    # SOCKS4 通常不需要用户名密码
    weight: 5
    region: "US"
    provider: "LegacyProxy"
    max_concurrent: 2
    enabled: false                 # 暂时禁用

  # 本地代理示例（如 Shadowsocks）
  - name: "local_shadowsocks"
    host: "127.0.0.1"
    port: 1087
    proxy_type: "socks5"
    weight: 20                     # 本地代理通常速度最快
    region: "Local"
    provider: "Shadowsocks"
    max_concurrent: 10
    enabled: true

  # 企业代理示例
  - name: "corporate_proxy"
    host: "proxy.company.com"
    port: 3128
    proxy_type: "http"
    username: "employee_id"
    password: "company_password"
    weight: 12
    region: "Corporate"
    provider: "Company"
    max_concurrent: 5
    enabled: true

# =============================================================================
# 代理池配置
# =============================================================================

pool_settings:
  # 代理选择策略
  selection_strategy: "weighted_random"  # 选项: random, round_robin, weighted_random, fastest
  
  # 健康检查配置
  health_check:
    enabled: true
    interval: 300                        # 检查间隔（秒）
    timeout: 10                          # 检查超时（秒）
    test_url: "https://httpbin.org/ip"   # 测试 URL
    max_failures: 3                      # 最大失败次数
    retry_interval: 600                  # 重试间隔（秒）
  
  # 轮换配置
  rotation:
    enabled: true
    interval: 10                         # 轮换间隔（秒）
    max_requests_per_proxy: 100          # 每个代理最大请求数
    force_rotation_on_error: true        # 出错时强制轮换
  
  # 故障转移配置
  failover:
    enabled: true
    max_retries: 3                       # 最大重试次数
    backoff_factor: 2                    # 退避因子
    circuit_breaker_threshold: 5         # 熔断器阈值

# =============================================================================
# 地区优先级配置
# =============================================================================

region_preferences:
  # 按优先级排序的地区列表
  priority_order:
    - "Local"      # 本地代理优先级最高
    - "US"         # 美国代理
    - "EU"         # 欧洲代理
    - "Asia"       # 亚洲代理
    - "Corporate"  # 企业代理
  
  # 地区权重调整
  region_weights:
    Local: 2.0     # 本地代理权重翻倍
    US: 1.5        # 美国代理权重增加50%
    EU: 1.2        # 欧洲代理权重增加20%
    Asia: 1.0      # 亚洲代理正常权重
    Corporate: 0.8 # 企业代理权重降低20%

# =============================================================================
# 认证配置
# =============================================================================

authentication:
  # 是否使用加密存储认证信息
  use_encrypted_storage: true
  
  # 认证信息存储文件
  credentials_file: ".proxy_credentials"
  
  # 支持的认证方式
  supported_methods:
    - "basic"      # 基础认证（用户名密码）
    - "digest"     # 摘要认证
    - "ntlm"       # NTLM 认证（企业环境）

# =============================================================================
# 监控和日志配置
# =============================================================================

monitoring:
  # 是否启用代理性能监控
  enabled: true
  
  # 监控指标
  metrics:
    - "response_time"    # 响应时间
    - "success_rate"     # 成功率
    - "bandwidth_usage"  # 带宽使用
    - "connection_count" # 连接数
  
  # 日志配置
  logging:
    level: "INFO"
    include_request_details: false  # 不记录请求详情（安全考虑）
    include_response_headers: true  # 记录响应头
    log_proxy_rotation: true        # 记录代理轮换

# =============================================================================
# 安全配置
# =============================================================================

security:
  # SSL/TLS 配置
  ssl_verification: true           # 是否验证 SSL 证书
  ssl_cert_file: null             # 自定义证书文件路径
  
  # 代理链配置（多级代理）
  proxy_chain:
    enabled: false                 # 是否启用代理链
    max_chain_length: 2           # 最大代理链长度
  
  # IP 泄露防护
  ip_leak_protection:
    enabled: true
    check_dns_leaks: true         # 检查 DNS 泄露
    check_webrtc_leaks: true      # 检查 WebRTC 泄露
  
  # 用户代理轮换
  user_agent_rotation:
    enabled: true
    agents_file: "config/user_agents.txt"

# =============================================================================
# 性能优化配置
# =============================================================================

performance:
  # 连接池配置
  connection_pool:
    max_connections: 100          # 最大连接数
    max_connections_per_host: 10  # 每个主机最大连接数
    keepalive_timeout: 30         # 保持连接超时
  
  # 请求配置
  request_settings:
    timeout: 30                   # 请求超时
    max_redirects: 3              # 最大重定向次数
    chunk_size: 8192             # 数据块大小
  
  # 缓存配置
  caching:
    enabled: true
    cache_size: 1000             # 缓存大小
    cache_ttl: 3600              # 缓存生存时间（秒）
