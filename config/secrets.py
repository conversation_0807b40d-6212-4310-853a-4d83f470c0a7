"""安全的密钥管理模块"""

import os
import base64
from typing import Optional, Dict, Any
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class SecretManager:
    """密钥管理器，用于安全存储和检索敏感信息"""
    
    def __init__(self, key_file: str = ".secret_key"):
        self.key_file = Path(key_file)
        self._cipher = None
        self._initialize_cipher()
    
    def _initialize_cipher(self) -> None:
        """初始化加密器"""
        if self.key_file.exists():
            # 从文件读取密钥
            with open(self.key_file, 'rb') as f:
                key = f.read()
        else:
            # 生成新密钥
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            # 设置文件权限（仅所有者可读写）
            os.chmod(self.key_file, 0o600)
        
        self._cipher = Fernet(key)
    
    def encrypt_secret(self, secret: str) -> str:
        """加密密钥"""
        if not self._cipher:
            raise RuntimeError("加密器未初始化")
        
        encrypted = self._cipher.encrypt(secret.encode())
        return base64.b64encode(encrypted).decode()
    
    def decrypt_secret(self, encrypted_secret: str) -> str:
        """解密密钥"""
        if not self._cipher:
            raise RuntimeError("加密器未初始化")
        
        try:
            encrypted_bytes = base64.b64decode(encrypted_secret.encode())
            decrypted = self._cipher.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            raise ValueError(f"解密失败: {e}")
    
    def store_secret(self, name: str, value: str, secrets_file: str = ".secrets") -> None:
        """存储加密的密钥到文件"""
        secrets_path = Path(secrets_file)
        
        # 读取现有密钥
        secrets = {}
        if secrets_path.exists():
            with open(secrets_path, 'r') as f:
                for line in f:
                    if '=' in line:
                        key, val = line.strip().split('=', 1)
                        secrets[key] = val
        
        # 添加新密钥
        secrets[name] = self.encrypt_secret(value)
        
        # 写回文件
        with open(secrets_path, 'w') as f:
            for key, val in secrets.items():
                f.write(f"{key}={val}\n")
        
        # 设置文件权限
        os.chmod(secrets_path, 0o600)
    
    def get_secret(self, name: str, secrets_file: str = ".secrets") -> Optional[str]:
        """从文件获取解密的密钥"""
        secrets_path = Path(secrets_file)
        
        if not secrets_path.exists():
            return None
        
        with open(secrets_path, 'r') as f:
            for line in f:
                if line.startswith(f"{name}="):
                    encrypted_value = line.strip().split('=', 1)[1]
                    return self.decrypt_secret(encrypted_value)
        
        return None


class EnvironmentConfig:
    """环境配置管理器"""
    
    def __init__(self):
        self.secret_manager = SecretManager()
    
    def get_github_token(self) -> Optional[str]:
        """获取 GitHub Token，优先级：环境变量 > 加密文件 > 配置文件"""
        # 1. 环境变量
        token = os.getenv('GITHUB_TOKEN')
        if token:
            return token
        
        # 2. 加密存储的密钥
        token = self.secret_manager.get_secret('github_token')
        if token:
            return token
        
        # 3. 配置文件（不推荐，仅用于开发）
        return None
    
    def set_github_token(self, token: str) -> None:
        """安全存储 GitHub Token"""
        self.secret_manager.store_secret('github_token', token)
    
    def get_proxy_credentials(self, proxy_name: str) -> Optional[Dict[str, str]]:
        """获取代理认证信息"""
        username = self.secret_manager.get_secret(f'proxy_{proxy_name}_username')
        password = self.secret_manager.get_secret(f'proxy_{proxy_name}_password')
        
        if username and password:
            return {'username': username, 'password': password}
        
        return None
    
    def set_proxy_credentials(self, proxy_name: str, username: str, password: str) -> None:
        """设置代理认证信息"""
        self.secret_manager.store_secret(f'proxy_{proxy_name}_username', username)
        self.secret_manager.store_secret(f'proxy_{proxy_name}_password', password)
    
    def get_environment_type(self) -> str:
        """获取环境类型"""
        return os.getenv('ENVIRONMENT', 'development').lower()
    
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return self.get_environment_type() == 'production'
    
    def is_development(self) -> bool:
        """判断是否为开发环境"""
        return self.get_environment_type() == 'development'
    
    def get_config_overrides(self) -> Dict[str, Any]:
        """获取环境特定的配置覆盖"""
        overrides = {}
        
        # 生产环境配置
        if self.is_production():
            overrides.update({
                'log': {
                    'log_level': 'WARNING',
                    'max_log_size': 50 * 1024 * 1024,  # 50MB
                    'backup_count': 10
                },
                'search': {
                    'search_delay': 3.0,  # 更保守的延迟
                    'max_concurrent_searches': 2
                },
                'validation': {
                    'validation_delay': 0.5,
                    'max_concurrent': 2
                }
            })
        
        # 开发环境配置
        elif self.is_development():
            overrides.update({
                'log': {
                    'log_level': 'DEBUG'
                },
                'search': {
                    'search_delay': 1.0,
                    'max_results_per_query': 50  # 减少测试时的结果数量
                }
            })
        
        return overrides


# 全局实例
env_config = EnvironmentConfig()
