#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class SearchConfig:
    """搜索配置"""
    max_results_per_query: int = 100
    search_delay: float = 2.0
    max_concurrent_searches: int = 3
    enable_fork_search: bool = True
    enable_recent_search: bool = True


@dataclass
class ProxyConfig:
    """代理配置"""
    enabled: bool = False
    proxy_file: str = "config/proxy_config.yaml"
    rotation_interval: int = 10
    health_check_interval: int = 300
    max_failures: int = 5
    timeout: int = 30


@dataclass
class ValidationConfig:
    """验证配置"""
    max_concurrent: int = 3
    validation_delay: float = 0.2
    timeout: int = 30
    enable_deep_check: bool = True


@dataclass
class ReportConfig:
    """报告配置"""
    output_dir: str = "reports"
    generate_json: bool = True
    generate_markdown: bool = True
    generate_csv: bool = False
    include_invalid_tokens: bool = False


@dataclass
class LogConfig:
    """日志配置"""
    log_dir: str = "logs"
    log_level: str = "INFO"
    max_log_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


class Settings:
    """配置管理器"""

    def __init__(self, config_file: str = "config/settings.yaml"):
        self.config_file = Path(config_file)
        self.config_file.parent.mkdir(exist_ok=True)

        # 默认配置
        self.github_token: str = ""
        self.search = SearchConfig()
        self.proxy = ProxyConfig()
        self.validation = ValidationConfig()
        self.report = ReportConfig()
        self.log = LogConfig()

        # 加载配置
        self.load_config()

    def load_config(self) -> None:
        """加载配置文件"""
        if not self.config_file.exists():
            self.create_default_config()
            return

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 更新配置
            if 'github_token' in config_data:
                self.github_token = config_data['github_token']

            if 'search' in config_data:
                self.search = SearchConfig(**config_data['search'])

            if 'proxy' in config_data:
                self.proxy = ProxyConfig(**config_data['proxy'])

            if 'validation' in config_data:
                self.validation = ValidationConfig(**config_data['validation'])

            if 'report' in config_data:
                self.report = ReportConfig(**config_data['report'])

            if 'log' in config_data:
                self.log = LogConfig(**config_data['log'])

        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            self.create_default_config()

    def create_default_config(self) -> None:
        """创建默认配置文件"""
        default_config = {
            'github_token': 'your_github_token_here',
            'search': asdict(self.search),
            'proxy': asdict(self.proxy),
            'validation': asdict(self.validation),
            'report': asdict(self.report),
            'log': asdict(self.log)
        }

        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)

        print(f"📝 已创建默认配置文件: {self.config_file}")

    def get_github_token(self) -> str:
        """获取GitHub Token"""
        # 优先使用环境变量
        token = os.getenv('GITHUB_TOKEN') or self.github_token
        if not token or token == 'your_github_token_here':
            raise ValueError("请设置有效的 GitHub Token")
        return token

    def validate_config(self) -> bool:
        """验证配置"""
        try:
            # 验证 GitHub Token
            token = self.get_github_token()
            if not token or token == 'your_github_token_here':
                print("❌ 请设置有效的 GitHub Token")
                return False

            # 验证代理配置
            if self.proxy.enabled:
                proxy_file = Path(self.proxy.proxy_file)
                if not proxy_file.exists():
                    print(f"❌ 代理配置文件不存在: {proxy_file}")
                    return False

            # 验证输出目录
            output_dir = Path(self.report.output_dir)
            try:
                output_dir.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                print(f"❌ 无法创建输出目录 {output_dir}: {e}")
                return False

            # 验证日志目录
            log_dir = Path(self.log.log_dir)
            try:
                log_dir.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                print(f"❌ 无法创建日志目录 {log_dir}: {e}")
                return False

            return True

        except Exception as e:
            print(f"❌ 配置验证失败: {e}")
            return False


# 全局配置实例
settings = Settings()

