#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub Token Discovery and Validation System - 主程序
"""

import asyncio
import sys
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import settings
from utils.logger import SecurityLogger
from proxy.manager import ProxyManager
from search.engine import GitHubSearchEngine
from extraction.extractor import TokenExtractor
from validation.validator import TokenValidator
from reporting.generator import ReportGenerator
from core.models import SystemStats


class GitHubTokenHunter:
    """GitHub Token 猎人主类"""

    def __init__(self):
        self.logger = SecurityLogger("GitHubTokenHunter")
        self.stats = SystemStats()

        # 初始化组件
        self.proxy_manager = None
        self.search_engine = None
        self.token_extractor = None
        self.token_validator = None
        self.report_generator = None

        self._initialize_components()

    def _initialize_components(self):
        """初始化所有组件"""
        try:
            # 验证配置
            if not settings.validate_config():
                self.logger.critical("❌ 配置验证失败，请检查GitHub Token配置")
                sys.exit(1)

            # 初始化代理管理器
            if settings.proxy.enabled:
                self.proxy_manager = ProxyManager(self.logger)
                self.logger.info("📡 代理功能已启用")

            # 初始化GitHub搜索引擎
            github_token = settings.get_github_token()
            self.search_engine = GitHubSearchEngine(github_token, self.logger, self.proxy_manager)

            # 初始化Token提取器
            self.token_extractor = TokenExtractor(self.logger)

            # 初始化Token验证器
            self.token_validator = TokenValidator(self.logger, self.proxy_manager)

            # 初始化报告生成器
            self.report_generator = ReportGenerator(self.logger, settings.report)

            self.logger.info("✅ 所有组件初始化完成")

        except Exception as e:
            self.logger.critical(f"❌ 组件初始化失败: {str(e)}")
            sys.exit(1)

    async def run_discovery_process(self):
        """运行完整的发现流程"""
        self.logger.info("🚀 开始GitHub Token发现与验证流程...")

        try:
            # 1. 代理健康检查
            if self.proxy_manager:
                await self.proxy_manager.check_all_proxies_health()

            # 2. 执行搜索
            self.logger.info("🔍 第1阶段: 搜索潜在的Token文件...")

            try:
                search_results = await self.search_engine.search_all_queries()
                self.stats.total_searches = len(search_results)
            except KeyboardInterrupt:
                self.logger.warning("⚠️ 搜索阶段被用户中断")
                return
            except Exception as e:
                self.logger.error(f"❌ 搜索阶段失败: {str(e)}")
                return

            if not search_results:
                self.logger.warning("⚠️ 未发现任何潜在的Token文件")
                return

            # 继续其他步骤...

        except KeyboardInterrupt:
            self.logger.warning("⚠️ 用户中断程序执行")
        except Exception as e:
            self.logger.critical(f"❌ 程序执行失败: {str(e)}")
        finally:
            await self._cleanup()

    async def _generate_comprehensive_report(self, tokens, validation_results):
        """生成综合报告"""
        try:
            await self.report_generator.generate_full_report(
                tokens, validation_results, self.stats
            )
        except Exception as e:
            self.logger.error(f"❌ 报告生成失败: {str(e)}")

    def _display_final_summary(self):
        """显示最终统计摘要"""
        self.logger.info("\n" + "=" * 60)
        self.logger.info("📊 **执行总结报告**")
        self.logger.info("=" * 60)
        self.logger.info(f"🔍 总搜索结果: {self.stats.total_searches}")
        self.logger.info(f"🎯 发现Token数: {self.stats.total_tokens_found}")
        self.logger.info(f"✅ 有效Token数: {self.stats.valid_tokens}")
        self.logger.info(f"🚨 高危Token数: {self.stats.high_risk_tokens}")
        self.logger.info(f"⏱️ 总运行时间: {self.stats.runtime}")

        if self.proxy_manager:
            proxy_stats = self.proxy_manager.get_stats()
            self.logger.info(f"📡 代理切换次数: {proxy_stats['total']}")
            self.logger.info(f"🔄 活跃代理数: {proxy_stats['active']}")

        self.logger.info("=" * 60)

        # 安全建议
        if self.stats.valid_tokens > 0:
            self.logger.warning("\n🚨 **安全警告**")
            self.logger.warning(f"发现 {self.stats.valid_tokens} 个有效的GitHub Token，请立即:")
            self.logger.warning("1. 联系相关开发人员立即撤销这些Token")
            self.logger.warning("2. 检查这些Token的使用记录")
            self.logger.warning("3. 更新相关系统的认证配置")
            self.logger.warning("4. 检查相关仓库的提交历史")

    async def _cleanup(self):
        """清理资源"""
        try:
            # 清理搜索引擎资源
            if hasattr(self, 'search_engine') and self.search_engine:
                await self.search_engine._close_session()

            # 清理其他资源
            if hasattr(self, 'proxy_manager') and self.proxy_manager:
                # 如果代理管理器有清理方法
                if hasattr(self.proxy_manager, 'cleanup'):
                    await self.proxy_manager.cleanup()

            self.logger.info("🧹 资源清理完成")

        except Exception as e:
            self.logger.error(f"❌ 资源清理失败: {str(e)}")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="GitHub Token 发现与验证系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py                           # 使用默认配置运行
  python main.py --proxy                   # 启用代理模式
  python main.py --config custom.yaml     # 使用自定义配置
  python main.py --output-dir ./reports   # 指定报告输出目录
        """
    )

    parser.add_argument(
        '--config', '-c',
        help='指定配置文件路径',
        default='config/settings.yaml'
    )

    parser.add_argument(
        '--proxy', '-p',
        action='store_true',
        help='强制启用代理模式'
    )

    parser.add_argument(
        '--output-dir', '-o',
        help='指定报告输出目录',
        default='reports'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='设置日志级别'
    )

    parser.add_argument(
        '--max-tokens',
        type=int,
        default=1000,
        help='最大处理Token数量限制'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='试运行模式（不执行实际验证）'
    )

    return parser.parse_args()


async def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()

    # 应用命令行参数
    if args.proxy:
        settings.proxy.enabled = True

    if args.output_dir:
        settings.report.output_dir = args.output_dir

    if args.log_level:
        settings.log.log_level = args.log_level

    # 创建输出目录
    Path(settings.report.output_dir).mkdir(parents=True, exist_ok=True)
    Path(settings.log.log_dir).mkdir(parents=True, exist_ok=True)

    # 显示启动信息
    print("\n" + "=" * 70)
    print("🔍 **GitHub Token 发现与验证系统 v2.0**")
    print("=" * 70)
    print("⚠️  **免责声明**: 此工具仅用于安全研究和授权测试")
    print("⚠️  **使用须知**: 请确保遵守相关法律法规和平台政策")
    print("=" * 70 + "\n")

    # 创建并运行主程序
    try:
        hunter = GitHubTokenHunter()
        await hunter.run_discovery_process()
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

    # 运行主程序
    asyncio.run(main())
