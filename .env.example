# GitHub Token 发现与验证系统 - 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# =============================================================================
# GitHub 配置
# =============================================================================

# GitHub Personal Access Token (必需)
# 获取地址: https://github.com/settings/tokens
# 所需权限: public_repo (用于搜索公共仓库)
GITHUB_TOKEN=your_github_token_here

# GitHub API 基础 URL (可选，默认为官方 API)
# GITHUB_API_BASE_URL=https://api.github.com

# =============================================================================
# 环境配置
# =============================================================================

# 运行环境: development, staging, production
ENVIRONMENT=development

# 调试模式 (development 环境建议开启)
DEBUG=true

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# 日志目录
LOG_DIR=logs

# 最大日志文件大小 (字节)
MAX_LOG_SIZE=10485760

# 日志文件备份数量
LOG_BACKUP_COUNT=5

# =============================================================================
# 代理配置
# =============================================================================

# 是否启用代理
PROXY_ENABLED=false

# 代理配置文件路径
PROXY_CONFIG_FILE=config/proxy_config.yaml

# 代理轮换间隔 (秒)
PROXY_ROTATION_INTERVAL=10

# 代理健康检查间隔 (秒)
PROXY_HEALTH_CHECK_INTERVAL=300

# =============================================================================
# 搜索配置
# =============================================================================

# 每个查询的最大结果数
MAX_RESULTS_PER_QUERY=100

# 搜索延迟 (秒，避免触发速率限制)
SEARCH_DELAY=2.0

# 最大并发搜索数
MAX_CONCURRENT_SEARCHES=3

# 是否搜索 Fork 仓库
ENABLE_FORK_SEARCH=true

# 是否启用最近更新搜索
ENABLE_RECENT_SEARCH=true

# =============================================================================
# 验证配置
# =============================================================================

# 最大并发验证数
MAX_CONCURRENT_VALIDATIONS=3

# 验证延迟 (秒)
VALIDATION_DELAY=0.2

# 验证超时 (秒)
VALIDATION_TIMEOUT=30

# 是否启用深度检查
ENABLE_DEEP_CHECK=true

# =============================================================================
# 报告配置
# =============================================================================

# 报告输出目录
REPORT_OUTPUT_DIR=reports

# 是否生成 JSON 报告
GENERATE_JSON_REPORT=true

# 是否生成 Markdown 报告
GENERATE_MARKDOWN_REPORT=true

# 是否生成 CSV 报告
GENERATE_CSV_REPORT=false

# 是否包含无效 Token
INCLUDE_INVALID_TOKENS=false

# =============================================================================
# 性能配置
# =============================================================================

# 最大内存使用 (MB)
MAX_MEMORY_MB=500

# 批处理大小
BATCH_SIZE=10

# 连接池大小
CONNECTION_POOL_SIZE=10

# 请求超时 (秒)
REQUEST_TIMEOUT=30

# =============================================================================
# 安全配置
# =============================================================================

# 速率限制 - 每小时最大请求数
RATE_LIMIT_PER_HOUR=1000

# 是否启用安全审计
ENABLE_SECURITY_AUDIT=true

# 密钥文件路径
SECRET_KEY_FILE=.secret_key

# 加密密钥存储文件
ENCRYPTED_SECRETS_FILE=.secrets

# =============================================================================
# 数据库配置 (如果使用数据库存储)
# =============================================================================

# 数据库 URL (可选)
# DATABASE_URL=sqlite:///github_tokens.db

# 数据库连接池大小
# DB_POOL_SIZE=5

# =============================================================================
# 监控配置
# =============================================================================

# 是否启用性能监控
ENABLE_PERFORMANCE_MONITORING=true

# 监控数据保留天数
MONITORING_RETENTION_DAYS=30

# 是否启用内存监控
ENABLE_MEMORY_MONITORING=true

# =============================================================================
# 通知配置 (可选)
# =============================================================================

# Slack Webhook URL (用于通知)
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...

# 邮件通知配置
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password
# NOTIFICATION_EMAIL=<EMAIL>

# =============================================================================
# 开发配置
# =============================================================================

# 是否启用开发模式特性
DEV_MODE=true

# 测试数据目录
TEST_DATA_DIR=tests/data

# 是否跳过网络请求 (测试模式)
SKIP_NETWORK_REQUESTS=false

# Mock API 响应
USE_MOCK_RESPONSES=false
