#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token验证器模块
"""

import asyncio
import aiohttp
import time
from typing import List, Dict, Optional
from datetime import datetime

from core.models import TokenInfo, ValidationResult
from validation.risk_assessor import RiskAssessor
from utils.logger import SecurityLogger
from proxy.manager import ProxyManager
from config.settings import settings


class TokenValidator:
    """GitHub Token验证器"""

    def __init__(self, logger: SecurityLogger, proxy_manager: Optional[ProxyManager] = None):
        self.logger = logger
        self.proxy_manager = proxy_manager
        self.config = settings.validation
        self.risk_assessor = RiskAssessor()

        # GitHub API端点
        self.api_endpoints = {
            'user': 'https://api.github.com/user',
            'rate_limit': 'https://api.github.com/rate_limit',
            'user_repos': 'https://api.github.com/user/repos',
            'orgs': 'https://api.github.com/user/orgs'
        }

        self.validated_count = 0
        self.valid_tokens = []

    async def validate_tokens(self, tokens: List[TokenInfo]) -> List[ValidationResult]:
        """批量验证tokens"""
        self.logger.info(f"🔐 开始验证 {len(tokens)} 个tokens...")

        # 按优先级排序（高风险token优先验证）
        sorted_tokens = sorted(tokens, key=lambda t: self._get_priority_score(t), reverse=True)

        # 创建异步任务
        semaphore = asyncio.Semaphore(self.config.max_concurrent)

        async def validate_with_semaphore(token):
            async with semaphore:
                await asyncio.sleep(self.config.validation_delay)
                return await self._validate_single_token(token)

        # 执行验证
        validation_tasks = [validate_with_semaphore(token) for token in sorted_tokens]
        results = await asyncio.gather(*validation_tasks, return_exceptions=True)

        # 处理结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"❌ Token验证异常: {str(result)}")
                continue

            if result:
                valid_results.append(result)
                if result.is_valid:
                    self.valid_tokens.append(sorted_tokens[i])

        valid_count = sum(1 for r in valid_results if r.is_valid)
        self.logger.info(f"✅ 验证完成: {valid_count}/{len(valid_results)} 个有效token")

        return valid_results

    def _get_priority_score(self, token: TokenInfo) -> int:
        """计算token验证优先级分数"""
        scores = {
            'github_pat_fine_grained': 100,
            'github_pat_classic': 90,
            'github_app_installation': 80,
            'github_app_user': 70,
            'github_oauth': 60,
            'github_refresh': 50,
        }

        base_score = scores.get(token.token_type, 30)

        # 根据发现时间调整（最近发现的优先）
        hours_old = (datetime.now() - token.discovered_at).total_seconds() / 3600
        time_bonus = max(0, 20 - hours_old)

        return base_score + time_bonus

    async def _validate_single_token(self, token: TokenInfo) -> Optional[ValidationResult]:
        """验证单个token"""
        try:
            # 获取代理
            proxy = None
            if self.proxy_manager:
                proxy_info = self.proxy_manager.get_next_proxy()
                if proxy_info:
                    proxy = proxy_info.url

            # 执行验证请求
            user_info, rate_limit_info = await self._make_validation_requests(token.token, proxy)

            if user_info is None:
                return ValidationResult(
                    token_hash=token.hash_id,
                    is_valid=False,
                    user_info={},
                    permissions={},
                    risk_level='unknown',
                    risk_score=0,
                    concerns=[],
                    validated_at=datetime.now(),
                    rate_limit_info=rate_limit_info or {}
                )

            # 获取权限信息
            permissions = await self._get_token_permissions(token.token, proxy)

            # 风险评估
            risk_assessment = self.risk_assessor.assess_token_risk(
                token, user_info, permissions
            )

            # 标记代理成功
            if self.proxy_manager and self.proxy_manager.current_proxy:
                self.proxy_manager.mark_proxy_success(self.proxy_manager.current_proxy)

            self.validated_count += 1

            result = ValidationResult(
                token_hash=token.hash_id,
                is_valid=True,
                user_info=user_info,
                permissions=permissions,
                risk_level=risk_assessment['risk_level'],
                risk_score=risk_assessment['risk_score'],
                concerns=risk_assessment['concerns'],
                validated_at=datetime.now(),
                rate_limit_info=rate_limit_info or {}
            )

            self.logger.info(
                f"✅ Token验证成功: {token.hash_id} | "
                f"用户: {user_info.get('login', 'Unknown')} | "
                f"风险: {risk_assessment['risk_level']}"
            )

            return result

        except Exception as e:
            self.logger.error(f"❌ Token验证失败 {token.hash_id}: {str(e)}")

            # 标记代理失败
            if self.proxy_manager and self.proxy_manager.current_proxy:
                self.proxy_manager.mark_proxy_failed(self.proxy_manager.current_proxy, str(e))

            return None

    async def _make_validation_requests(self, token: str, proxy: Optional[str] = None) -> tuple:
        """执行验证请求"""
        headers = {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'SecurityResearch-TokenValidator/2.0'
        }

        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        connector = aiohttp.TCPConnector(ssl=False) if proxy else None

        try:
            async with aiohttp.ClientSession(
                    timeout=timeout,
                    connector=connector,
                    headers=headers
            ) as session:

                # 使用代理配置
                kwargs = {}
                if proxy:
                    kwargs['proxy'] = proxy

                # 获取用户信息
                async with session.get(self.api_endpoints['user'], **kwargs) as response:
                    if response.status == 200:
                        user_info = await response.json()
                    elif response.status == 401:
                        return None, None  # Token无效
                    else:
                        self.logger.warning(f"⚠️ 用户信息请求失败: {response.status}")
                        return None, None

                # 获取速率限制信息
                try:
                    async with session.get(self.api_endpoints['rate_limit'], **kwargs) as response:
                        if response.status == 200:
                            rate_limit_info = await response.json()
                        else:
                            rate_limit_info = {}
                except:
                    rate_limit_info = {}

                return user_info, rate_limit_info

        except Exception as e:
            self.logger.error(f"❌ 验证请求异常: {str(e)}")
            raise

    async def _get_token_permissions(self, token: str, proxy: Optional[str] = None) -> Dict:
        """获取token权限信息"""
        permissions = {
            'scopes': [],
            'can_access_repos': False,
            'can_access_orgs': False,
            'repo_count': 0,
            'org_count': 0
        }

        headers = {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'SecurityResearch-TokenValidator/2.0'
        }

        timeout = aiohttp.ClientTimeout(total=self.config.timeout)

        try:
            async with aiohttp.ClientSession(timeout=timeout, headers=headers) as session:
                kwargs = {'proxy': proxy} if proxy else {}

                # 测试仓库访问权限
                try:
                    async with session.get(self.api_endpoints['user_repos'], params={'per_page': 1},
                                           **kwargs) as response:
                        if response.status == 200:
                            permissions['can_access_repos'] = True
                            # 从响应头获取仓库总数
                            link_header = response.headers.get('Link', '')
                            if 'last' in link_header:
                                import re
                                last_page_match = re.search(r'page=(\d+)[^>]*>;\s*rel="last"', link_header)
                                if last_page_match:
                                    permissions['repo_count'] = int(last_page_match.group(1)) * 100  # 估算

                        # 获取scopes信息
                        scopes = response.headers.get('X-OAuth-Scopes', '')
                        if scopes:
                            permissions['scopes'] = [s.strip() for s in scopes.split(',')]
                except:
                    pass

                # 测试组织访问权限
                try:
                    async with session.get(self.api_endpoints['orgs'], **kwargs) as response:
                        if response.status == 200:
                            permissions['can_access_orgs'] = True
                            orgs_data = await response.json()
                            permissions['org_count'] = len(orgs_data)
                except:
                    pass

        except Exception as e:
            self.logger.debug(f"获取权限信息失败: {str(e)}")

        return permissions

    def get_validation_stats(self) -> Dict:
        """获取验证统计信息"""
        return {
            'total_validated': self.validated_count,
            'valid_tokens': len(self.valid_tokens),
            'validation_rate': len(self.valid_tokens) / self.validated_count if self.validated_count > 0 else 0
        }
