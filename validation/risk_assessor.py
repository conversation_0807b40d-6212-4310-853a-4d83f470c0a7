#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token风险评估器
"""

from typing import Dict, List
from datetime import datetime
from core.models import TokenInfo


class RiskAssessor:
    """Token风险评估器"""

    def __init__(self):
        self.risk_factors = {
            'token_type': {
                'github_pat_fine_grained': 90,
                'github_pat_classic': 85,
                'github_app_installation': 75,
                'github_app_user': 70,
                'github_oauth': 60,
                'github_refresh': 55,
                'github_token_legacy': 50,
            },
            'user_type': {
                'organization': 40,
                'user': 20,
                'bot': 10
            },
            'permissions': {
                'admin': 50,
                'write': 30,
                'read': 10
            }
        }

    def assess_token_risk(self, token: TokenInfo, user_info: Dict, permissions: Dict) -> Dict:
        """评估Token风险"""
        risk_score = 0
        concerns = []

        # 1. Token类型风险
        token_type_risk = self.risk_factors['token_type'].get(token.token_type, 30)
        risk_score += token_type_risk

        # 2. 用户类型风险
        user_type = user_info.get('type', 'user').lower()
        user_risk = self.risk_factors['user_type'].get(user_type, 20)
        risk_score += user_risk

        # 3. 权限风险评估
        scopes = permissions.get('scopes', [])
        high_risk_scopes = ['admin', 'delete_repo', 'repo', 'workflow', 'write:packages']

        for scope in scopes:
            if any(hr_scope in scope for hr_scope in high_risk_scopes):
                risk_score += 25
                concerns.append(f"高风险权限: {scope}")

        # 4. 仓库访问风险
        repo_count = permissions.get('repo_count', 0)
        if repo_count > 100:
            risk_score += 20
            concerns.append(f"可访问大量仓库: {repo_count}")
        elif repo_count > 10:
            risk_score += 10
            concerns.append(f"可访问多个仓库: {repo_count}")

        # 5. 组织访问风险
        if permissions.get('can_access_orgs', False):
            org_count = permissions.get('org_count', 0)
            if org_count > 0:
                risk_score += 30
                concerns.append(f"可访问组织资源: {org_count} 个组织")

        # 6. 用户声誉风险
        followers = user_info.get('followers', 0)
        public_repos = user_info.get('public_repos', 0)
        account_age_days = (datetime.now() - datetime.fromisoformat(
            user_info.get('created_at', '2020-01-01T00:00:00Z').replace('Z', '+00:00')
        )).days

        # 高声誉用户风险更高
        if followers > 1000 or public_repos > 50:
            risk_score += 25
            concerns.append("高声誉用户账户")

        # 新账户风险评估
        if account_age_days < 30:
            risk_score += 15
            concerns.append("新创建的账户")

        # 7. Token发现位置风险
        if any(keyword in token.source_file.lower() for keyword in ['.env', 'config', 'secret']):
            risk_score += 20
            concerns.append("在敏感配置文件中发现")

        if 'fork:true' in str(token.context):
            risk_score += 15
            concerns.append("在Fork仓库中发现")

        # 8. 上下文分析风险
        context_lower = token.context.lower()
        risk_keywords = ['production', 'prod', 'live', 'master', 'main', 'deploy', 'ci', 'cd']

        for keyword in risk_keywords:
            if keyword in context_lower:
                risk_score += 10
                concerns.append(f"生产环境相关: {keyword}")
                break

        # 确定风险等级
        risk_level = self._calculate_risk_level(risk_score)

        return {
            'risk_score': min(risk_score, 100),  # 最高100分
            'risk_level': risk_level,
            'concerns': concerns,
            'assessment_details': {
                'token_type_score': token_type_risk,
                'user_type_score': user_risk,
                'permissions_assessment': self._assess_permissions(permissions),
                'user_profile_risk': self._assess_user_profile(user_info),
                'discovery_context_risk': self._assess_discovery_context(token)
            }
        }

    def _calculate_risk_level(self, risk_score: int) -> str:
        """根据分数计算风险等级"""
        if risk_score >= 80:
            return 'critical'
        elif risk_score >= 60:
            return 'high'
        elif risk_score >= 40:
            return 'medium'
        else:
            return 'low'

    def _assess_permissions(self, permissions: Dict) -> Dict:
        """详细权限风险评估"""
        assessment = {
            'scope_risk': 'low',
            'repo_access_risk': 'low',
            'org_access_risk': 'low'
        }

        # 权限范围风险
        dangerous_scopes = ['admin', 'delete_repo', 'repo', 'workflow']
        scopes = permissions.get('scopes', [])

        if any(scope in ' '.join(scopes) for scope in dangerous_scopes):
            assessment['scope_risk'] = 'high'
        elif any(scope in ' '.join(scopes) for scope in ['write', 'push']):
            assessment['scope_risk'] = 'medium'

        # 仓库访问风险
        repo_count = permissions.get('repo_count', 0)
        if repo_count > 50:
            assessment['repo_access_risk'] = 'high'
        elif repo_count > 10:
            assessment['repo_access_risk'] = 'medium'

        # 组织访问风险
        if permissions.get('can_access_orgs', False) and permissions.get('org_count', 0) > 0:
            assessment['org_access_risk'] = 'high'

        return assessment

    def _assess_user_profile(self, user_info: Dict) -> Dict:
        """用户档案风险评估"""
        return {
            'account_type': user_info.get('type', 'unknown'),
            'followers_count': user_info.get('followers', 0),
            'public_repos_count': user_info.get('public_repos', 0),
            'company': user_info.get('company', ''),
            'is_high_profile': user_info.get('followers', 0) > 1000,
            'has_organization': user_info.get('company') is not None
        }

    def _assess_discovery_context(self, token: TokenInfo) -> Dict:
        """发现上下文风险评估"""
        return {
            'source_type': self._classify_source_file(token.source_file),
            'in_production_context': self._is_production_context(token.context),
            'in_configuration_file': self._is_config_file(token.source_file),
            'repository_type': self._classify_repository(token.source_file)
        }

    def _classify_source_file(self, source_file: str) -> str:
        """分类源文件类型"""
        filename = source_file.lower()

        if any(ext in filename for ext in ['.env', '.config', '.yml', '.yaml', '.json']):
            return 'configuration'
        elif any(ext in filename for ext in ['.py', '.js', '.go', '.java', '.rb']):
            return 'source_code'
        elif 'dockerfile' in filename or 'docker-compose' in filename:
            return 'container'
        elif '.github/workflows' in filename:
            return 'ci_cd'
        else:
            return 'other'

    def _is_production_context(self, context: str) -> bool:
        """判断是否为生产环境上下文"""
        prod_keywords = ['production', 'prod', 'live', 'deploy', 'release']
        return any(keyword in context.lower() for keyword in prod_keywords)

    def _is_config_file(self, source_file: str) -> bool:
        """判断是否为配置文件"""
        config_indicators = ['.env', '.config', '.yml', '.yaml', '.json', 'settings']
        return any(indicator in source_file.lower() for indicator in config_indicators)

    def _classify_repository(self, source_file: str) -> str:
        """分类仓库类型"""
        repo_name = source_file.split('/')[0] if '/' in source_file else source_file

        # 根据仓库名称模式判断
        if any(pattern in repo_name.lower() for pattern in ['fork', 'clone', 'copy']):
            return 'fork'
        elif any(pattern in repo_name.lower() for pattern in ['test', 'demo', 'example']):
            return 'test'
        elif any(pattern in repo_name.lower() for pattern in ['private', 'internal', 'company']):
            return 'private'
        else:
            return 'public'
