"""Core interfaces for dependency injection and testing"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from core.models import (
    SearchQuery, SearchResult, TokenInfo, ValidationResult, 
    ProxyInfo, SecurityReport
)


class ILogger(ABC):
    """Logger interface"""
    
    @abstractmethod
    def info(self, message: str) -> None: ...
    
    @abstractmethod
    def warning(self, message: str) -> None: ...
    
    @abstractmethod
    def error(self, message: str) -> None: ...
    
    @abstractmethod
    def debug(self, message: str) -> None: ...
    
    @abstractmethod
    def critical(self, message: str) -> None: ...


class IProxyManager(ABC):
    """Proxy manager interface"""
    
    @abstractmethod
    async def get_proxy(self) -> Optional[ProxyInfo]: ...
    
    @abstractmethod
    async def check_all_proxies_health(self) -> None: ...
    
    @abstractmethod
    def mark_proxy_failed(self, proxy: ProxyInfo) -> None: ...


class ISearchEngine(ABC):
    """Search engine interface"""
    
    @abstractmethod
    async def search_all_queries(self) -> List[SearchResult]: ...
    
    @abstractmethod
    async def search_query(self, query: SearchQuery) -> List[SearchResult]: ...


class ITokenExtractor(ABC):
    """Token extractor interface"""
    
    @abstractmethod
    async def extract_from_results(self, results: List[SearchResult]) -> List[TokenInfo]: ...
    
    @abstractmethod
    async def extract_from_text(self, text: str, source: str) -> List[TokenInfo]: ...


class ITokenValidator(ABC):
    """Token validator interface"""
    
    @abstractmethod
    async def validate_tokens(self, tokens: List[TokenInfo]) -> List[ValidationResult]: ...
    
    @abstractmethod
    async def validate_single_token(self, token: TokenInfo) -> ValidationResult: ...


class IReportGenerator(ABC):
    """Report generator interface"""
    
    @abstractmethod
    async def generate_report(
        self, 
        tokens: List[TokenInfo], 
        validations: List[ValidationResult]
    ) -> SecurityReport: ...
    
    @abstractmethod
    async def save_report(self, report: SecurityReport) -> str: ...


class IConfigManager(ABC):
    """Configuration manager interface"""
    
    @abstractmethod
    def get_github_token(self) -> str: ...
    
    @abstractmethod
    def validate_config(self) -> bool: ...
    
    @abstractmethod
    def reload_config(self) -> None: ...


class ISecurityScanner(ABC):
    """Main security scanner interface"""
    
    @abstractmethod
    async def run_scan(self) -> SecurityReport: ...
    
    @abstractmethod
    async def run_discovery_process(self) -> None: ...
