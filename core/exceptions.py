"""Enhanced exception hierarchy with detailed error context"""

from typing import Optional, Dict, Any
from datetime import datetime


class GitHubSearchTokensError(Exception):
    """Base exception for all application errors"""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}
        self.cause = cause
        self.timestamp = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging/reporting"""
        return {
            'error_type': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'context': self.context,
            'timestamp': self.timestamp.isoformat(),
            'cause': str(self.cause) if self.cause else None
        }


class ConfigurationError(GitHubSearchTokensError):
    """Configuration-related errors"""
    pass


class ProxyError(GitHubSearchTokensError):
    """Errors related to proxy operations"""
    pass


class SearchError(GitHubSearchTokensError):
    """Errors related to search operations"""
    pass


class ExtractionError(GitHubSearchTokensError):
    """Errors during token extraction"""
    pass


class ValidationError(GitHubSearchTokensError):
    """Errors during token validation"""
    pass


class ReportGenerationError(GitHubSearchTokensError):
    """Errors during report generation"""
    pass


class RateLimitError(SearchError):
    """GitHub API rate limit exceeded"""

    def __init__(self, reset_time: Optional[datetime] = None, **kwargs):
        super().__init__(**kwargs)
        self.reset_time = reset_time


class AuthenticationError(SearchError):
    """GitHub authentication failed"""
    pass


class NetworkError(GitHubSearchTokensError):
    """Network-related errors"""
    pass