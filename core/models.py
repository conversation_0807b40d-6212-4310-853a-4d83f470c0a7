#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心数据模型
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Dict, Optional, Any
import hashlib


@dataclass
class SearchQuery:
    """搜索查询模型"""
    query_type: str
    priority: str
    search_terms: str

    def __post_init__(self):
        """初始化后处理"""
        if self.priority not in ['low', 'medium', 'high', 'critical']:
            raise ValueError(f"Invalid priority: {self.priority}")


@dataclass
class SearchResult:
    """搜索结果模型"""
    query_type: str
    priority: str
    file_url: str
    download_url: str
    repository: str
    file_path: str
    file_name: str
    sha: str
    size: int

    def __post_init__(self):
        """初始化后处理"""
        if not self.file_url:
            self.file_url = ""
        if not self.download_url:
            self.download_url = ""


@dataclass
class TokenInfo:
    """Token信息模型"""
    token: str
    token_type: str
    source_url: str
    source_file: str
    context: str
    discovered_at: datetime

    def __post_init__(self):
        """初始化后处理"""
        self._hash_id = None

    @property
    def hash_id(self) -> str:
        """生成Token的哈希ID（用于去重和匿名化）"""
        if self._hash_id is None:
            self._hash_id = hashlib.sha256(
                f"{self.token}_{self.source_file}".encode()
            ).hexdigest()[:16]
        return self._hash_id

    @property
    def masked_token(self) -> str:
        """返回掩码后的token（用于日志显示）"""
        if len(self.token) <= 8:
            return "*" * len(self.token)
        return f"{self.token[:4]}...{self.token[-4:]}"


@dataclass
class ValidationResult:
    """验证结果模型"""
    token_hash: str
    is_valid: bool
    user_info: Optional[Dict[str, Any]] = None
    permissions: Optional[Dict[str, Any]] = None
    risk_score: int = 0
    risk_level: str = "unknown"
    concerns: List[str] = field(default_factory=list)
    validated_at: datetime = field(default_factory=datetime.now)
    error_message: Optional[str] = None


@dataclass
class ProxyInfo:
    """代理信息模型"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    proxy_type: str = "http"  # http, https, socks4, socks5
    is_active: bool = True
    response_time: Optional[float] = None
    last_tested: Optional[datetime] = None
    success_count: int = 0
    failure_count: int = 0

    def __post_init__(self):
        """初始化后处理"""
        if self.proxy_type not in ['http', 'https', 'socks4', 'socks5']:
            raise ValueError(f"Unsupported proxy type: {self.proxy_type}")

    @property
    def url(self) -> str:
        """生成代理URL"""
        if self.username and self.password:
            auth = f"{self.username}:{self.password}@"
        else:
            auth = ""

        return f"{self.proxy_type}://{auth}{self.host}:{self.port}"

    @property
    def success_rate(self) -> float:
        """计算成功率"""
        total = self.success_count + self.failure_count
        if total == 0:
            return 0.0
        return self.success_count / total

    @property
    def is_healthy(self) -> bool:
        """判断代理是否健康"""
        return (
                self.is_active and
                self.success_rate > 0.5 and
                (self.response_time is None or self.response_time < 10.0)
        )


@dataclass
class SystemStats:
    """系统统计模型"""
    total_searches: int = 0
    total_tokens_found: int = 0
    valid_tokens: int = 0
    high_risk_tokens: int = 0
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None

    @property
    def runtime(self) -> str:
        """计算运行时间"""
        end = self.end_time or datetime.now()
        duration = end - self.start_time

        hours, remainder = divmod(duration.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)

        if hours > 0:
            return f"{int(hours)}h {int(minutes)}m {int(seconds)}s"
        elif minutes > 0:
            return f"{int(minutes)}m {int(seconds)}s"
        else:
            return f"{int(seconds)}s"


@dataclass
class ReportSection:
    """报告章节模型"""
    title: str
    content: str
    priority: str = "medium"

    def __post_init__(self):
        if self.priority not in ['low', 'medium', 'high', 'critical']:
            self.priority = 'medium'


@dataclass
class SecurityReport:
    """安全报告模型"""
    report_id: str
    title: str
    generated_at: datetime
    sections: List[ReportSection] = field(default_factory=list)
    summary: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)

    def add_section(self, title: str, content: str, priority: str = "medium"):
        """添加报告章节"""
        section = ReportSection(title=title, content=content, priority=priority)
        self.sections.append(section)

    def add_recommendation(self, recommendation: str):
        """添加建议"""
        self.recommendations.append(recommendation)
