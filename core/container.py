"""Dependency Injection Container"""

from typing import Dict, Any, TypeVar, Type, Callable, Optional
from abc import ABC, abstractmethod
import inspect

T = TypeVar('T')


class Container:
    """Simple dependency injection container"""
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._singletons: Dict[str, Any] = {}
    
    def register_singleton(self, interface: Type[T], implementation: Type[T]) -> None:
        """Register a singleton service"""
        key = interface.__name__
        self._factories[key] = implementation
        
    def register_transient(self, interface: Type[T], implementation: Type[T]) -> None:
        """Register a transient service"""
        key = interface.__name__
        self._services[key] = implementation
        
    def register_instance(self, interface: Type[T], instance: T) -> None:
        """Register a specific instance"""
        key = interface.__name__
        self._singletons[key] = instance
        
    def resolve(self, interface: Type[T]) -> T:
        """Resolve a service instance"""
        key = interface.__name__
        
        # Check for existing singleton
        if key in self._singletons:
            return self._singletons[key]
            
        # Check for factory (singleton pattern)
        if key in self._factories:
            instance = self._create_instance(self._factories[key])
            self._singletons[key] = instance
            return instance
            
        # Check for transient
        if key in self._services:
            return self._create_instance(self._services[key])
            
        raise ValueError(f"Service {key} not registered")
    
    def _create_instance(self, cls: Type[T]) -> T:
        """Create instance with dependency injection"""
        sig = inspect.signature(cls.__init__)
        kwargs = {}
        
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue
                
            if param.annotation != inspect.Parameter.empty:
                try:
                    kwargs[param_name] = self.resolve(param.annotation)
                except ValueError:
                    # If dependency not found, skip if has default
                    if param.default == inspect.Parameter.empty:
                        raise
                        
        return cls(**kwargs)


# Global container instance
container = Container()
