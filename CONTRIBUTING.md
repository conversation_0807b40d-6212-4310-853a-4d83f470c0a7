# 贡献指南

感谢您对 GitHub Token 发现与验证系统的贡献兴趣！本文档将指导您如何参与项目开发。

## 🚀 快速开始

### 开发环境设置

1. **Fork 并克隆仓库**
```bash
git clone https://github.com/your-username/github-search-tokens.git
cd github-search-tokens
```

2. **创建虚拟环境**
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows
```

3. **安装开发依赖**
```bash
pip install -e ".[dev]"
```

4. **设置 pre-commit hooks**
```bash
pre-commit install
```

5. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，添加你的 GitHub Token
```

### 验证安装

```bash
# 运行测试
pytest

# 检查代码格式
black --check .
isort --check-only .

# 类型检查
mypy .

# 安全扫描
bandit -r .
```

## 📝 开发流程

### 1. 创建功能分支

```bash
git checkout -b feature/your-feature-name
# 或
git checkout -b bugfix/issue-number
```

### 2. 开发规范

#### 代码风格
- 遵循 PEP 8 规范
- 使用 Black 进行代码格式化
- 使用 isort 整理导入语句
- 行长度限制为 100 字符

#### 类型提示
```python
# 好的示例
def process_tokens(tokens: List[TokenInfo]) -> List[ValidationResult]:
    """处理 Token 列表并返回验证结果"""
    results: List[ValidationResult] = []
    # ... 实现
    return results

# 避免
def process_tokens(tokens):
    # 缺少类型提示
    pass
```

#### 文档字符串
```python
def validate_token(token: str, token_type: str) -> bool:
    """
    验证 Token 格式是否正确
    
    Args:
        token: 要验证的 Token 字符串
        token_type: Token 类型（如 'github_pat'）
    
    Returns:
        bool: Token 格式是否有效
    
    Raises:
        ValueError: 当 token_type 不支持时
    
    Example:
        >>> validate_token("ghp_" + "x" * 36, "github_pat")
        True
    """
    # 实现
```

### 3. 测试要求

#### 测试覆盖率
- 新功能必须有对应的单元测试
- 测试覆盖率应保持在 80% 以上
- 关键安全功能需要 100% 覆盖率

#### 测试类型
```python
import pytest

# 单元测试
@pytest.mark.unit
def test_token_validation():
    """测试 Token 验证功能"""
    pass

# 集成测试
@pytest.mark.integration
async def test_search_engine_integration():
    """测试搜索引擎集成"""
    pass

# 安全测试
@pytest.mark.security
def test_input_sanitization():
    """测试输入清理功能"""
    pass
```

#### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定标记的测试
pytest -m unit
pytest -m integration
pytest -m security

# 生成覆盖率报告
pytest --cov=. --cov-report=html
```

### 4. 提交规范

#### 提交消息格式
```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

**类型 (type):**
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例:**
```
feat(search): 添加新的 Token 搜索模式

- 支持 AWS 访问密钥检测
- 改进正则表达式匹配精度
- 添加相应的单元测试

Closes #123
```

### 5. Pull Request 流程

#### 提交前检查清单
- [ ] 代码通过所有测试
- [ ] 代码格式符合规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 提交消息符合规范

#### PR 描述模板
```markdown
## 变更描述
简要描述这个 PR 的目的和变更内容。

## 变更类型
- [ ] Bug 修复
- [ ] 新功能
- [ ] 重大变更
- [ ] 文档更新

## 测试
- [ ] 添加了新的测试
- [ ] 所有测试通过
- [ ] 手动测试通过

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 自我审查了代码
- [ ] 添加了必要的注释
- [ ] 更新了文档

## 相关 Issue
Closes #(issue number)
```

## 🏗️ 架构指南

### 模块设计原则

1. **单一职责**: 每个模块只负责一个明确的功能
2. **依赖注入**: 使用接口和容器管理依赖
3. **异步优先**: 优先使用异步操作提高性能
4. **错误处理**: 完善的异常处理和恢复机制

### 添加新功能

#### 1. 定义接口
```python
# core/interfaces.py
class INewFeature(ABC):
    @abstractmethod
    async def process(self, data: Any) -> Any:
        """处理数据"""
        pass
```

#### 2. 实现功能
```python
# new_module/processor.py
from core.interfaces import INewFeature

class NewFeatureProcessor(INewFeature):
    async def process(self, data: Any) -> Any:
        # 实现逻辑
        pass
```

#### 3. 注册依赖
```python
# core/container.py
container.register_singleton(INewFeature, NewFeatureProcessor)
```

#### 4. 添加测试
```python
# tests/test_new_feature.py
@pytest.mark.unit
class TestNewFeatureProcessor:
    def test_process(self):
        # 测试实现
        pass
```

## 🔍 代码审查指南

### 审查要点

1. **功能正确性**: 代码是否实现了预期功能
2. **安全性**: 是否存在安全漏洞
3. **性能**: 是否有性能问题
4. **可维护性**: 代码是否易于理解和维护
5. **测试覆盖**: 是否有足够的测试

### 常见问题

#### 安全问题
- 硬编码的密钥或敏感信息
- 未验证的用户输入
- 不安全的文件操作
- 缺少速率限制

#### 性能问题
- 同步 I/O 操作
- 内存泄漏
- 不必要的循环
- 缺少缓存

## 📚 资源链接

- [Python 代码风格指南](https://pep8.org/)
- [异步编程最佳实践](https://docs.python.org/3/library/asyncio.html)
- [安全编程指南](https://owasp.org/www-project-secure-coding-practices-quick-reference-guide/)
- [测试最佳实践](https://docs.pytest.org/en/stable/goodpractices.html)

## 🤝 社区

- 💬 [讨论区](https://github.com/your-org/github-search-tokens/discussions)
- 🐛 [问题跟踪](https://github.com/your-org/github-search-tokens/issues)
- 📧 [邮件列表](mailto:<EMAIL>)

## 📄 许可证

通过贡献代码，您同意您的贡献将在 MIT 许可证下授权。

---

感谢您的贡献！🎉
