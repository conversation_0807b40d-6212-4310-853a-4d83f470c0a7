#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索查询定义模块
"""

from typing import List, Dict


class SearchQueries:
    """搜索查询管理"""

    def __init__(self):
        self.queries = self._load_all_queries()

    def _load_all_queries(self) -> List[Dict]:
        """加载所有搜索查询"""
        return [
            # 高优先级 - 新格式Token
            {"query": "github_pat_ in:file", "type": "fine_grained_pat", "priority": "critical"},
            {"query": "ghp_ in:file", "type": "classic_pat", "priority": "critical"},
            {"query": "ghs_ in:file", "type": "app_installation", "priority": "critical"},

            # 高优先级 - 环境文件
            {"query": "github_pat_ in:file filename:.env", "type": "env_file", "priority": "critical"},
            {"query": "ghp_ in:file filename:.env", "type": "env_file", "priority": "critical"},
            {"query": "GITHUB_TOKEN in:file filename:.env", "type": "env_file", "priority": "critical"},

            # 高优先级 - CI/CD配置
            {"query": "GITHUB_TOKEN in:file path:.github/workflows", "type": "cicd", "priority": "high"},
            {"query": "github_pat_ in:file path:.github/workflows", "type": "cicd", "priority": "high"},
            {"query": "secrets.GITHUB_TOKEN in:file", "type": "cicd", "priority": "high"},

            # 高优先级 - Fork仓库（高命中率）
            {"query": "github_pat_ in:file fork:true", "type": "fork", "priority": "high"},
            {"query": "ghp_ in:file fork:true", "type": "fork", "priority": "high"},

            # 高优先级 - 最近更新
            {"query": "github_pat_ in:file pushed:>2024-11-01", "type": "recent", "priority": "high"},
            {"query": "ghp_ in:file pushed:>2024-11-01", "type": "recent", "priority": "high"},

            # 中等优先级 - 其他Token类型
            {"query": "ghu_ in:file", "type": "app_user", "priority": "medium"},
            {"query": "gho_ in:file", "type": "oauth", "priority": "medium"},
            {"query": "ghr_ in:file", "type": "refresh", "priority": "medium"},

            # 中等优先级 - 环境变量
            {"query": "GITHUB_TOKEN in:file", "type": "env_var", "priority": "medium"},
            {"query": "GITHUB_ACCESS_TOKEN in:file", "type": "env_var", "priority": "medium"},
            {"query": "GH_TOKEN in:file", "type": "env_var", "priority": "medium"},
            {"query": "GITHUB_PAT in:file", "type": "env_var", "priority": "medium"},

            # 中等优先级 - 配置文件
            {"query": "github_pat_ in:file filename:config.yaml", "type": "config", "priority": "medium"},
            {"query": "GITHUB_TOKEN in:file filename:config.json", "type": "config", "priority": "medium"},
            {"query": "ghp_ in:file filename:settings.py", "type": "config", "priority": "medium"},

            # 中等优先级 - Docker配置
            {"query": "GITHUB_TOKEN in:file filename:Dockerfile", "type": "container", "priority": "medium"},
            {"query": "github_pat_ in:file filename:docker-compose.yml", "type": "container", "priority": "medium"},

            # 中等优先级 - 脚本文件
            {"query": "ghp_ in:file extension:sh", "type": "script", "priority": "medium"},
            {"query": "github_pat_ in:file extension:py", "type": "script", "priority": "medium"},
            {"query": "GITHUB_TOKEN in:file extension:js", "type": "script", "priority": "medium"},

            # 低优先级 - 测试和示例
            {"query": "github_pat_ in:file path:test", "type": "test", "priority": "low"},
            {"query": "GITHUB_TOKEN in:file path:examples", "type": "example", "priority": "low"},
            {"query": "ghp_ in:file filename:README.md", "type": "readme", "priority": "low"},

            # 特殊搜索 - 泄露模式
            {"query": "\"ghp_\" OR \"github_pat_\" in:file size:<1000", "type": "small_files", "priority": "medium"},
            {"query": "token in:file filename:.gitignore", "type": "gitignore", "priority": "low"},

            # 组织和企业相关
            {"query": "github_pat_ in:file user:github", "type": "enterprise", "priority": "high"},
            {"query": "GITHUB_TOKEN in:file org:microsoft", "type": "enterprise", "priority": "high"},
        ]

    def get_all_queries(self) -> List[Dict]:
        """获取所有查询"""
        return self.queries

    def get_prioritized_queries(self) -> List[Dict]:
        """获取按优先级排序的查询"""
        priority_order = {"critical": 0, "high": 1, "medium": 2, "low": 3}
        return sorted(self.queries, key=lambda x: priority_order.get(x["priority"], 4))

    def get_queries_by_priority(self, priority: str) -> List[Dict]:
        """获取指定优先级的查询"""
        return [q for q in self.queries if q["priority"] == priority]

    def get_queries_by_type(self, query_type: str) -> List[Dict]:
        """获取指定类型的查询"""
        return [q for q in self.queries if q["type"] == query_type]

