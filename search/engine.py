#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
from typing import List, Optional
from datetime import datetime, timedelta
import time

from core.models import SearchQuery, SearchResult
from utils.logger import SecurityLogger
from proxy.manager import ProxyManager


class GitHubSearchEngine:
    """GitHub搜索引擎"""

    def __init__(self, github_token: str, logger: SecurityLogger, proxy_manager: Optional[ProxyManager] = None):
        self.github_token = github_token
        self.logger = logger
        self.proxy_manager = proxy_manager

        # API配置
        self.api_base = "https://api.github.com"
        self.rate_limit_remaining = 5000
        self.rate_limit_reset_time = None

        # 请求配置
        self.timeout = aiohttp.ClientTimeout(total=30)
        self.max_retries = 3
        self.session = None  # 初始化为None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._create_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._close_session()

    async def _create_session(self):
        """创建HTTP会话"""
        if self.session is None:
            self.session = aiohttp.ClientSession(
                timeout=self.timeout,
                headers=self._get_headers()
            )

    async def _close_session(self):
        """关闭HTTP会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    async def search_all_queries(self) -> List[SearchResult]:
        """执行所有搜索查询"""
        queries = self._get_search_queries()
        self.logger.info(f"🔍 开始执行 {len(queries)} 个搜索查询...")

        all_results = []
        failed_queries = []

        # 确保创建会话
        await self._create_session()

        try:
            # 分批处理，避免过度并发
            batch_size = 5  # 减少并发数
            for i in range(0, len(queries), batch_size):
                batch = queries[i:i + batch_size]

                self.logger.info(f"🔄 处理第 {i // batch_size + 1} 批查询 ({len(batch)} 个)")

                # 检查速率限制
                await self._check_rate_limit()

                # 处理当前批次
                batch_results = await self._process_query_batch(batch)

                for result in batch_results:
                    if isinstance(result, Exception):
                        failed_queries.append(str(result))
                        continue
                    if result:
                        all_results.extend(result)

                # 批次间延迟
                if i + batch_size < len(queries):
                    await asyncio.sleep(2)

        except KeyboardInterrupt:
            self.logger.warning("⚠️ 搜索被用户中断")
            raise
        except Exception as e:
            self.logger.error(f"❌ 搜索执行失败: {str(e)}")
            raise
        finally:
            await self._close_session()

        self.logger.info(f"✅ 搜索完成: {len(all_results)} 个结果, {len(failed_queries)} 个失败")
        return all_results

    async def _process_query_batch(self, queries: List[SearchQuery]) -> List:
        """处理查询批次"""
        tasks = []

        for query in queries:
            task = asyncio.create_task(
                self._execute_single_query_with_retry(query),
                name=f"search_{query.query_type}"
            )
            tasks.append(task)

        try:
            # 等待所有任务完成，但允许取消
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return results
        except asyncio.CancelledError:
            # 取消所有未完成的任务
            for task in tasks:
                if not task.done():
                    task.cancel()

            # 等待任务清理
            await asyncio.gather(*tasks, return_exceptions=True)
            raise

    async def _execute_single_query_with_retry(self, query: SearchQuery) -> Optional[List[SearchResult]]:
        """执行单个查询（带重试）"""
        for attempt in range(self.max_retries):
            try:
                return await self._execute_single_query(query)
            except asyncio.CancelledError:
                raise
            except Exception as e:
                if attempt == self.max_retries - 1:
                    self.logger.error(f"❌ 查询最终失败 {query.query_type}: {str(e)}")
                    return None

                wait_time = 2 ** attempt  # 指数退避
                self.logger.warning(f"⚠️ 查询失败，{wait_time}秒后重试 ({attempt + 1}/{self.max_retries})")
                await asyncio.sleep(wait_time)

        return None

    async def _execute_single_query(self, query: SearchQuery) -> List[SearchResult]:
        """执行单个搜索查询"""
        results = []
        page = 1

        # 确保会话存在
        if self.session is None or self.session.closed:
            await self._create_session()

        while page <= 3:  # 限制页数避免过多请求
            try:
                # 检查是否被取消
                current_task = asyncio.current_task()
                if current_task and current_task.cancelled():
                    raise asyncio.CancelledError()

                # 检查速率限制
                await self._check_rate_limit()

                # 构建请求URL
                url = f"{self.api_base}/search/code"
                params = {
                    'q': query.search_terms,
                    'per_page': 30,  # 减少每页结果数
                    'page': page
                }

                # 发送请求
                async with self.session.get(url, params=params) as response:
                    # 更新速率限制信息
                    self._update_rate_limit_info(response)

                    if response.status == 200:
                        data = await response.json()
                        items = data.get('items', [])

                        if not items:
                            break

                        # 转换为SearchResult对象
                        page_results = self._convert_to_search_results(items, query)
                        results.extend(page_results)

                        self.logger.debug(f"📄 {query.query_type} 第{page}页: {len(page_results)} 结果")

                        # 检查是否有更多页面
                        if len(items) < params['per_page']:
                            break

                    elif response.status == 403:
                        response_text = await response.text()
                        if 'rate limit' in response_text.lower():
                            self.logger.warning("⏸️ 触发API速率限制")
                            await self._wait_for_rate_limit_reset()
                            continue
                        else:
                            self.logger.error(f"❌ 查询被拒绝: {response.status}")
                            break

                    elif response.status == 422:
                        self.logger.warning(f"⚠️ 查询格式错误: {query.search_terms}")
                        break

                    else:
                        self.logger.warning(f"⚠️ 意外响应状态: {response.status}")
                        break

                page += 1

                # 页面间延迟
                await asyncio.sleep(1)

            except asyncio.CancelledError:
                raise
            except Exception as e:
                self.logger.error(f"❌ 查询执行异常 {query.query_type} 第{page}页: {str(e)}")
                break

        return results

    async def _check_rate_limit(self):
        """检查并处理速率限制"""
        if self.rate_limit_remaining <= 10:  # 预留缓冲
            if self.rate_limit_reset_time:
                wait_time = (self.rate_limit_reset_time - datetime.now()).total_seconds()
                if wait_time > 0:
                    self.logger.warning(f"⏸️ API速率限制，等待 {wait_time:.1f} 秒...")
                    await asyncio.sleep(min(wait_time, 60))  # 最多等待60秒

    async def _wait_for_rate_limit_reset(self):
        """等待速率限制重置"""
        if self.rate_limit_reset_time:
            wait_time = (self.rate_limit_reset_time - datetime.now()).total_seconds()
            if wait_time > 0:
                self.logger.info(f"⏱️ 等待速率限制重置: {wait_time:.1f} 秒")
                await asyncio.sleep(min(wait_time + 5, 120))  # 最多等待2分钟

    def _update_rate_limit_info(self, response):
        """更新速率限制信息"""
        try:
            self.rate_limit_remaining = int(response.headers.get('X-RateLimit-Remaining', 0))
            reset_timestamp = int(response.headers.get('X-RateLimit-Reset', 0))

            if reset_timestamp:
                self.rate_limit_reset_time = datetime.fromtimestamp(reset_timestamp)

            self.logger.debug(f"📊 剩余API调用: {self.rate_limit_remaining}")

        except (ValueError, TypeError):
            pass

    def _get_headers(self) -> dict:
        """获取请求头"""
        return {
            'Authorization': f'token {self.github_token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'SecurityResearch-GitHubSearch/2.0'
        }

    def _get_search_queries(self) -> List[SearchQuery]:
        """获取搜索查询列表"""
        # 修复：使用正确的参数名称
        high_value_queries = [
            # GitHub Token模式 - 优先级最高
            SearchQuery(
                query_type="github_token",
                priority="high",
                search_terms="ghp_ OR github_pat_ filename:env"
            ),
            SearchQuery(
                query_type="github_token",
                priority="high",
                search_terms="ghp_ OR ghs_ OR gho_ OR ghu_ OR ghr_"
            ),
            SearchQuery(
                query_type="github_token",
                priority="high",
                search_terms="github_token= filename:config"
            ),
            SearchQuery(
                query_type="github_token",
                priority="high",
                search_terms="GITHUB_TOKEN filename:yml"
            ),
            SearchQuery(
                query_type="github_token",
                priority="high",
                search_terms="Authorization: token gh"
            ),

            # 配置文件中的token
            SearchQuery(
                query_type="config_files",
                priority="medium",
                search_terms="token filename:.env"
            ),
            SearchQuery(
                query_type="config_files",
                priority="medium",
                search_terms="api_key filename:config.yml"
            ),
            SearchQuery(
                query_type="config_files",
                priority="medium",
                search_terms="access_token filename:.yaml"
            ),

            # CI/CD配置
            SearchQuery(
                query_type="ci_cd",
                priority="medium",
                search_terms="github.com/settings/tokens filename:.github"
            ),
            SearchQuery(
                query_type="ci_cd",
                priority="medium",
                search_terms="secrets.GITHUB_TOKEN filename:workflow"
            ),
        ]

        return high_value_queries

    def _convert_to_search_results(self, items: List[dict], query: SearchQuery) -> List[SearchResult]:
        """转换API响应为SearchResult对象"""
        results = []

        for item in items:
            try:
                result = SearchResult(
                    query_type=query.query_type,
                    priority=query.priority,
                    file_url=item.get('html_url', ''),
                    download_url=item.get('download_url', ''),
                    repository=item.get('repository', {}).get('full_name', ''),
                    file_path=item.get('path', ''),
                    file_name=item.get('name', ''),
                    sha=item.get('sha', ''),
                    size=item.get('size', 0)
                )
                results.append(result)

            except Exception as e:
                self.logger.debug(f"⚠️ 结果转换失败: {str(e)}")
                continue

        return results
