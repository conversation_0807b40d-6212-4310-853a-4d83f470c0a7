"""pytest 配置和共享 fixtures"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, Generator
from unittest.mock import Mock, AsyncMock

from core.models import SearchResult, TokenInfo, ProxyInfo, ValidationResult
from core.interfaces import ILogger, IProxyManager, ISearchEngine
from config.settings import Settings


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """创建临时目录"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def mock_logger() -> Mock:
    """模拟日志记录器"""
    logger = Mock(spec=ILogger)
    logger.info = Mock()
    logger.warning = Mock()
    logger.error = Mock()
    logger.debug = Mock()
    logger.critical = Mock()
    return logger


@pytest.fixture
def sample_config() -> Dict[str, Any]:
    """示例配置"""
    return {
        'github_token': 'ghp_' + 'x' * 36,
        'search': {
            'max_results_per_query': 50,
            'search_delay': 1.0,
            'max_concurrent_searches': 2,
            'enable_fork_search': True,
            'enable_recent_search': True
        },
        'proxy': {
            'enabled': False,
            'proxy_file': 'config/proxy_config.yaml',
            'rotation_interval': 10,
            'health_check_interval': 300,
            'max_failures': 5,
            'timeout': 30
        },
        'validation': {
            'max_concurrent': 2,
            'validation_delay': 0.2,
            'timeout': 30,
            'enable_deep_check': True
        },
        'report': {
            'output_dir': 'reports',
            'generate_json': True,
            'generate_markdown': True,
            'generate_csv': False,
            'include_invalid_tokens': False
        },
        'log': {
            'log_dir': 'logs',
            'log_level': 'INFO',
            'max_log_size': 10485760,
            'backup_count': 5
        }
    }


@pytest.fixture
def test_settings(temp_dir: Path, sample_config: Dict[str, Any]) -> Settings:
    """测试用配置实例"""
    config_file = temp_dir / "test_settings.yaml"
    
    # 创建测试配置文件
    import yaml
    with open(config_file, 'w') as f:
        yaml.dump(sample_config, f)
    
    return Settings(str(config_file))


@pytest.fixture
def sample_search_results() -> list[SearchResult]:
    """示例搜索结果"""
    return [
        SearchResult(
            query_type="config_files",
            priority="high",
            file_url="https://github.com/test/repo/blob/main/config.yml",
            download_url="https://raw.githubusercontent.com/test/repo/main/config.yml",
            repository="test/repo",
            file_path="config.yml",
            file_name="config.yml",
            sha="abc123",
            size=1024
        ),
        SearchResult(
            query_type="env_files",
            priority="medium",
            file_url="https://github.com/test/repo2/blob/main/.env",
            download_url="https://raw.githubusercontent.com/test/repo2/main/.env",
            repository="test/repo2",
            file_path=".env",
            file_name=".env",
            sha="def456",
            size=512
        )
    ]


@pytest.fixture
def sample_tokens() -> list[TokenInfo]:
    """示例 Token 信息"""
    from datetime import datetime
    
    return [
        TokenInfo(
            token="ghp_" + "x" * 36,
            token_type="github_pat",
            source_url="https://github.com/test/repo/blob/main/config.yml",
            source_file="config.yml",
            context="github_token: ghp_" + "x" * 36,
            discovered_at=datetime.now()
        ),
        TokenInfo(
            token="AKIA" + "Y" * 16,
            token_type="aws_access_key",
            source_url="https://github.com/test/repo2/blob/main/.env",
            source_file=".env",
            context="AWS_ACCESS_KEY_ID=AKIA" + "Y" * 16,
            discovered_at=datetime.now()
        )
    ]


@pytest.fixture
def sample_proxy_info() -> ProxyInfo:
    """示例代理信息"""
    from datetime import datetime
    
    return ProxyInfo(
        host="127.0.0.1",
        port=8080,
        proxy_type="http",
        is_active=True,
        response_time=0.5,
        last_tested=datetime.now(),
        success_count=10,
        failure_count=2
    )


@pytest.fixture
def mock_proxy_manager(sample_proxy_info: ProxyInfo) -> Mock:
    """模拟代理管理器"""
    manager = Mock(spec=IProxyManager)
    manager.get_proxy = AsyncMock(return_value=sample_proxy_info)
    manager.check_all_proxies_health = AsyncMock()
    manager.mark_proxy_failed = Mock()
    return manager


@pytest.fixture
def mock_search_engine(sample_search_results: list[SearchResult]) -> Mock:
    """模拟搜索引擎"""
    engine = Mock(spec=ISearchEngine)
    engine.search_all_queries = AsyncMock(return_value=sample_search_results)
    engine.search_query = AsyncMock(return_value=sample_search_results[:1])
    return engine


@pytest.fixture
def sample_validation_results(sample_tokens: list[TokenInfo]) -> list[ValidationResult]:
    """示例验证结果"""
    from datetime import datetime
    
    return [
        ValidationResult(
            token_hash=sample_tokens[0].hash_id,
            is_valid=True,
            user_info={"login": "testuser", "id": 12345},
            permissions={"repo": True, "user": True},
            risk_score=8,
            risk_level="high",
            concerns=["Public repository access", "User data access"],
            validated_at=datetime.now()
        ),
        ValidationResult(
            token_hash=sample_tokens[1].hash_id,
            is_valid=False,
            risk_score=0,
            risk_level="unknown",
            error_message="Invalid token format",
            validated_at=datetime.now()
        )
    ]


@pytest.fixture
def github_api_responses() -> Dict[str, Any]:
    """GitHub API 响应示例"""
    return {
        'search_code': {
            'total_count': 2,
            'incomplete_results': False,
            'items': [
                {
                    'name': 'config.yml',
                    'path': 'config.yml',
                    'sha': 'abc123',
                    'url': 'https://api.github.com/repos/test/repo/contents/config.yml',
                    'html_url': 'https://github.com/test/repo/blob/main/config.yml',
                    'download_url': 'https://raw.githubusercontent.com/test/repo/main/config.yml',
                    'size': 1024,
                    'repository': {
                        'full_name': 'test/repo',
                        'private': False
                    }
                }
            ]
        },
        'user': {
            'login': 'testuser',
            'id': 12345,
            'type': 'User',
            'site_admin': False
        },
        'user_repos': {
            'total_count': 5,
            'repositories': []
        }
    }


class MockResponse:
    """模拟 HTTP 响应"""
    
    def __init__(self, json_data: Dict[str, Any], status_code: int = 200):
        self.json_data = json_data
        self.status_code = status_code
        self.status = status_code
    
    async def json(self):
        return self.json_data
    
    async def text(self, encoding='utf-8', errors='ignore'):
        import json
        return json.dumps(self.json_data)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


@pytest.fixture
def mock_aiohttp_session():
    """模拟 aiohttp 会话"""
    session = AsyncMock()
    session.get = AsyncMock()
    session.post = AsyncMock()
    return session
