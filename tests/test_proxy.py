"""代理管理器测试"""

import pytest
from unittest.mock import Mock, patch, mock_open
from pathlib import Path

from core.models import ProxyInfo
from core.exceptions import ProxyError


class TestProxyInfo:
    """ProxyInfo 模型测试"""

    def test_proxy_info_creation(self):
        """测试代理信息创建"""
        proxy = ProxyInfo(
            host="127.0.0.1",
            port=8080,
            proxy_type="http"
        )

        assert proxy.host == "127.0.0.1"
        assert proxy.port == 8080
        assert proxy.proxy_type == "http"
        assert proxy.is_active is True

    def test_proxy_url_generation(self):
        """测试代理 URL 生成"""
        # 无认证
        proxy = ProxyInfo(host="127.0.0.1", port=8080)
        assert proxy.url == "http://127.0.0.1:8080"

        # 有认证
        proxy_with_auth = ProxyInfo(
            host="127.0.0.1",
            port=8080,
            username="user",
            password="pass"
        )
        assert proxy_with_auth.url == "*******************************"

    def test_success_rate_calculation(self):
        """测试成功率计算"""
        proxy = ProxyInfo(host="127.0.0.1", port=8080)

        # 初始状态
        assert proxy.success_rate == 0.0

        # 设置统计数据
        proxy.success_count = 8
        proxy.failure_count = 2
        assert proxy.success_rate == 0.8

    def test_health_check(self):
        """测试健康检查"""
        proxy = ProxyInfo(host="127.0.0.1", port=8080)

        # 健康状态
        proxy.success_count = 9
        proxy.failure_count = 1
        proxy.response_time = 2.0
        assert proxy.is_healthy is True

        # 不健康状态 - 成功率低
        proxy.success_count = 3
        proxy.failure_count = 7
        assert proxy.is_healthy is False

        # 不健康状态 - 响应时间长
        proxy.success_count = 9
        proxy.failure_count = 1
        proxy.response_time = 15.0
        assert proxy.is_healthy is False

    def test_invalid_proxy_type(self):
        """测试无效代理类型"""
        with pytest.raises(ValueError, match="Unsupported proxy type"):
            ProxyInfo(host="127.0.0.1", port=8080, proxy_type="invalid")


@pytest.mark.asyncio
class TestProxyManager:
    """代理管理器测试"""

    @patch('proxy.manager.Path.exists')
    @patch('builtins.open', new_callable=mock_open, read_data="""
proxies:
  - host: 127.0.0.1
    port: 8080
    proxy_type: http
  - host: 127.0.0.1
    port: 8081
    proxy_type: http
""")
    def test_load_proxies_from_config(self, mock_file, mock_exists, mock_logger):
        """测试从配置文件加载代理"""
        mock_exists.return_value = True

        from proxy.manager import ProxyManager
        manager = ProxyManager(mock_logger)

        proxies = manager.get_all_proxies()
        assert len(proxies) == 2
        assert proxies[0].host == "127.0.0.1"
        assert proxies[0].port == 8080

    def test_empty_proxy_list(self, mock_logger):
        """测试空代理列表"""
        from proxy.manager import ProxyManager

        with patch('proxy.manager.Path.exists', return_value=False):
            manager = ProxyManager(mock_logger)
            proxies = manager.get_all_proxies()
            assert len(proxies) == 0

    async def test_get_healthy_proxy(self, mock_logger, sample_proxy_info):
        """测试获取健康代理"""
        from proxy.manager import ProxyManager

        manager = ProxyManager(mock_logger)
        manager.proxies = [sample_proxy_info]

        with patch('proxy.checker.is_proxy_healthy', return_value=True):
            proxy = await manager.get_proxy()
            assert proxy is not None
            assert proxy.host == "127.0.0.1"

    async def test_no_healthy_proxy(self, mock_logger):
        """测试无健康代理可用"""
        from proxy.manager import ProxyManager

        manager = ProxyManager(mock_logger)
        unhealthy_proxy = ProxyInfo(host="127.0.0.1", port=8080)
        unhealthy_proxy.is_active = False
        manager.proxies = [unhealthy_proxy]

        proxy = await manager.get_proxy()
        assert proxy is None

    def test_mark_proxy_failed(self, mock_logger, sample_proxy_info):
        """测试标记代理失败"""
        from proxy.manager import ProxyManager

        manager = ProxyManager(mock_logger)
        manager.proxies = [sample_proxy_info]

        initial_failures = sample_proxy_info.failure_count
        manager.mark_proxy_failed(sample_proxy_info)

        assert sample_proxy_info.failure_count == initial_failures + 1

