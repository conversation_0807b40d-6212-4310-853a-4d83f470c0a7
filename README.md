# GitHub Token 发现与验证系统

[![Python Version](https://img.shields.io/badge/python-3.10%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Security](https://img.shields.io/badge/security-audited-brightgreen.svg)](#安全考量)

一个专业的 GitHub Token 发现、验证和风险评估工具，用于识别代码仓库中可能泄露的敏感凭据。

## ✨ 主要特性

- 🔍 **智能搜索**: 使用多种搜索策略发现潜在的 Token 泄露
- 🛡️ **安全验证**: 安全地验证 Token 有效性，不会触发安全警报
- 📊 **风险评估**: 基于权限和使用情况评估 Token 风险等级
- 🔄 **代理支持**: 支持代理轮换，避免 IP 封禁
- 📈 **详细报告**: 生成多格式安全报告（JSON、Markdown、CSV）
- ⚡ **高性能**: 异步并发处理，支持大规模扫描
- 🔐 **安全设计**: 内置多层安全防护和审计日志

## 🚀 快速开始

### 环境要求

- Python 3.10+
- GitHub Personal Access Token

### 安装

```bash
# 克隆仓库
git clone https://github.com/your-org/github-search-tokens.git
cd github-search-tokens

# 安装依赖
pip install -e .

# 或使用开发模式
pip install -e ".[dev]"
```

### 基本配置

1. **设置 GitHub Token**:
```bash
# 方法1: 环境变量（推荐）
export GITHUB_TOKEN="your_github_token_here"

# 方法2: 加密存储
python -c "from config.secrets import env_config; env_config.set_github_token('your_token')"
```

2. **配置文件**:
```bash
# 复制示例配置
cp config/settings.yaml.example config/settings.yaml

# 编辑配置文件
vim config/settings.yaml
```

### 运行扫描

```bash
# 基本扫描
python main.py

# 使用代理
python main.py --proxy

# 自定义配置
python main.py --config custom_config.yaml

# 指定输出目录
python main.py --output-dir ./security_reports
```

## 📖 详细文档

### 配置说明

#### GitHub Token 配置
```yaml
# config/settings.yaml
github_token: "your_token_here"  # 不推荐，请使用环境变量

search:
  max_results_per_query: 100
  search_delay: 2.0
  max_concurrent_searches: 3
  enable_fork_search: true
  enable_recent_search: true

proxy:
  enabled: false
  proxy_file: "config/proxy_config.yaml"
  rotation_interval: 10
  health_check_interval: 300
```

#### 代理配置
```yaml
# config/proxy_config.yaml
proxies:
  - host: "127.0.0.1"
    port: 7890
    proxy_type: "http"
    username: "optional_user"
    password: "optional_pass"
```

### 使用示例

#### 基本 Python API
```python
from main import GitHubTokenScanner
from config.settings import settings

# 创建扫描器实例
scanner = GitHubTokenScanner()

# 运行扫描
await scanner.run_discovery_process()

# 获取结果
report = scanner.get_final_report()
```

#### 自定义搜索
```python
from search.engine import GitHubSearchEngine
from search.queries import SearchQueryBuilder

# 创建搜索引擎
engine = GitHubSearchEngine(github_token, logger)

# 自定义搜索查询
builder = SearchQueryBuilder()
queries = builder.build_custom_queries(["config", "env", "secret"])

# 执行搜索
results = await engine.search_queries(queries)
```

## 🔧 开发指南

### 开发环境设置

```bash
# 安装开发依赖
pip install -e ".[dev]"

# 安装 pre-commit hooks
pre-commit install

# 运行代码格式化
black .
isort .

# 运行类型检查
mypy .

# 运行安全扫描
bandit -r .
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试类型
pytest -m unit          # 单元测试
pytest -m integration   # 集成测试
pytest -m security      # 安全测试

# 生成覆盖率报告
pytest --cov=. --cov-report=html
```

### 项目结构

```
├── config/              # 配置管理
│   ├── settings.py     # 配置类定义
│   ├── secrets.py      # 密钥管理
│   └── *.yaml          # 配置文件
├── core/               # 核心模块
│   ├── models.py       # 数据模型
│   ├── exceptions.py   # 异常定义
│   ├── interfaces.py   # 接口抽象
│   └── container.py    # 依赖注入
├── proxy/              # 代理管理
├── search/             # 搜索引擎
├── extraction/         # Token 提取
├── validation/         # Token 验证
├── reporting/          # 报告生成
├── utils/              # 工具模块
│   ├── logger.py       # 日志系统
│   ├── security.py     # 安全工具
│   ├── performance.py  # 性能监控
│   └── validators.py   # 数据验证
└── tests/              # 测试套件
```

## 🛡️ 安全考量

### 数据保护
- ✅ Token 自动脱敏和哈希处理
- ✅ 加密存储敏感配置
- ✅ 安全的文件权限设置
- ✅ 审计日志记录

### 网络安全
- ✅ 速率限制防止 API 滥用
- ✅ 代理轮换避免 IP 封禁
- ✅ 输入验证防止注入攻击
- ✅ 安全的 HTTP 客户端配置

### 运行时安全
- ✅ 内存使用监控和清理
- ✅ 异常处理和错误恢复
- ✅ 资源限制和超时控制
- ✅ 安全事件检测和报警

## 📊 性能优化

### 并发控制
- 异步 I/O 操作
- 连接池管理
- 批量处理优化
- 内存使用监控

### 扩展性
- 模块化架构设计
- 插件式组件系统
- 配置驱动的行为
- 水平扩展支持

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
- 遵循 PEP 8 代码风格
- 添加类型提示
- 编写单元测试
- 更新文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本工具仅用于安全研究和授权的安全测试。使用者需要：

1. 确保拥有目标系统的合法授权
2. 遵守相关法律法规和服务条款
3. 负责任地披露发现的安全问题
4. 不得将工具用于恶意目的

## 📞 支持与反馈

- 🐛 [报告 Bug](https://github.com/your-org/github-search-tokens/issues)
- 💡 [功能请求](https://github.com/your-org/github-search-tokens/issues)
- 📧 [联系我们](mailto:<EMAIL>)
- 📖 [文档](https://github-search-tokens.readthedocs.io)

---

**⭐ 如果这个项目对你有帮助，请给我们一个 Star！**
