[project]
name = "github-search-tokens"
version = "0.1.0"
description = "GitHub Token 发现与验证系统 - 安全扫描工具"
authors = [
    {name = "Security Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
keywords = ["security", "github", "token", "scanner", "vulnerability"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Security",
    "Topic :: Software Development :: Quality Assurance",
]

dependencies = [
    # 核心依赖
    "aiohttp>=3.9.0",
    "pyyaml>=6.0.1",
    "requests>=2.31.0",

    # 加密和安全
    "cryptography>=41.0.0",

    # 模板引擎
    "jinja2>=3.1.2",

    # 数据处理
    "pandas>=2.0.0",

    # 命令行界面
    "click>=8.1.0",
    "rich>=13.0.0",

    # 配置验证
    "pydantic>=2.0.0",

    # 异步支持
    "asyncio-throttle>=1.0.2",
]

[project.optional-dependencies]
dev = [
    # 测试工具
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",

    # 代码质量
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "bandit>=1.7.5",

    # 文档
    "sphinx>=7.0.0",
    "sphinx-rtd-theme>=1.3.0",

    # 开发工具
    "pre-commit>=3.3.0",
    "tox>=4.6.0",
]

security = [
    "safety>=2.3.0",
    "semgrep>=1.30.0",
]

performance = [
    "memory-profiler>=0.61.0",
    "py-spy>=0.3.14",
]

[project.urls]
Homepage = "https://github.com/your-org/github-search-tokens"
Repository = "https://github.com/your-org/github-search-tokens"
Documentation = "https://github-search-tokens.readthedocs.io"
"Bug Tracker" = "https://github.com/your-org/github-search-tokens/issues"

[project.scripts]
github-token-scanner = "main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["config", "core", "proxy", "search", "extraction", "validation", "reporting", "utils"]

[tool.black]
line-length = 100
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "aiohttp.*",
    "yaml.*",
    "cryptography.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "security: marks tests as security tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["config", "core", "proxy", "search", "extraction", "validation", "reporting", "utils"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection for tests
