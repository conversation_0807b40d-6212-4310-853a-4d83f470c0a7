#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token正则表达式模式定义
"""

import re
from typing import Dict


class TokenPatterns:
    """Token匹配模式管理"""

    def __init__(self):
        self.patterns = self._initialize_patterns()

    def _initialize_patterns(self) -> Dict[str, Dict]:
        """初始化所有token模式"""
        return {
            # GitHub Personal Access Token (新格式)
            'github_pat_fine_grained': {
                'regex': r'github_pat_([a-zA-Z0-9_]{82})',
                'type': 'github_pat_fine_grained',
                'min_length': 90,
                'max_length': 100,
                'risk_level': 'critical'
            },

            # GitHub Personal Access Token (经典格式)
            'github_pat_classic': {
                'regex': r'ghp_([a-zA-Z0-9]{36})',
                'type': 'github_pat_classic',
                'min_length': 40,
                'max_length': 45,
                'risk_level': 'critical'
            },

            # GitHub App Installation Token
            'github_app_installation': {
                'regex': r'ghs_([a-zA-Z0-9]{36})',
                'type': 'github_app_installation',
                'min_length': 40,
                'max_length': 45,
                'risk_level': 'high'
            },

            # GitHub App User Token
            'github_app_user': {
                'regex': r'ghu_([a-zA-Z0-9]{36})',
                'type': 'github_app_user',
                'min_length': 40,
                'max_length': 45,
                'risk_level': 'high'
            },

            # GitHub OAuth Token
            'github_oauth': {
                'regex': r'gho_([a-zA-Z0-9]{36})',
                'type': 'github_oauth',
                'min_length': 40,
                'max_length': 45,
                'risk_level': 'medium'
            },

            # GitHub Refresh Token
            'github_refresh': {
                'regex': r'ghr_([a-zA-Z0-9]{76})',
                'type': 'github_refresh',
                'min_length': 80,
                'max_length': 85,
                'risk_level': 'medium'
            },

            # 通用GitHub Token模式（带引号）
            'github_token_quoted': {
                'regex': r'["\']([gh][hpsouru]_[a-zA-Z0-9_]{36,})["\']',
                'type': 'github_token_generic',
                'min_length': 35,
                'max_length': 100,
                'risk_level': 'high'
            },

            # 环境变量格式
            'github_token_env': {
                'regex': r'GITHUB_TOKEN["\']?\s*[:=]\s*["\']?([gh][hpsouru]_[a-zA-Z0-9_]{36,})["\']?',
                'type': 'github_token_env',
                'min_length': 35,
                'max_length': 100,
                'risk_level': 'high'
            },

            # 配置文件格式
            'github_token_config': {
                'regex': r'(?:github_token|access_token|token)\s*[:=]\s*["\']?([gh][hpsouru]_[a-zA-Z0-9_]{36,})["\']?',
                'type': 'github_token_config',
                'min_length': 35,
                'max_length': 100,
                'risk_level': 'high'
            },

            # Authorization Header
            'github_token_header': {
                'regex': r'Authorization["\']?\s*:\s*["\']?(?:token\s+|Bearer\s+)?([gh][hpsouru]_[a-zA-Z0-9_]{36,})["\']?',
                'type': 'github_token_header',
                'min_length': 35,
                'max_length': 100,
                'risk_level': 'high'
            },

            # CURL命令中的token
            'github_token_curl': {
                'regex': r'curl.*-H\s+["\']Authorization:\s*(?:token\s+|Bearer\s+)?([gh][hpsouru]_[a-zA-Z0-9_]{36,})["\']',
                'type': 'github_token_curl',
                'min_length': 35,
                'max_length': 100,
                'risk_level': 'high'
            },

            # 旧格式GitHub Token (40字符)
            'github_token_legacy': {
                'regex': r'(?<![\w\-\.])([a-f0-9]{40})(?![\w\-\.])',
                'type': 'github_token_legacy',
                'min_length': 40,
                'max_length': 40,
                'risk_level': 'medium'
            },
        }

    def get_all_patterns(self) -> Dict[str, Dict]:
        """获取所有模式"""
        return self.patterns

    def get_pattern(self, pattern_name: str) -> Dict:
        """获取指定模式"""
        return self.patterns.get(pattern_name, {})

    def get_patterns_by_risk_level(self, risk_level: str) -> Dict[str, Dict]:
        """根据风险级别获取模式"""
        return {
            name: pattern
            for name, pattern in self.patterns.items()
            if pattern.get('risk_level') == risk_level
        }

    def validate_token_format(self, token: str, pattern_name: str) -> bool:
        """验证token是否符合指定模式"""
        pattern_info = self.get_pattern(pattern_name)
        if not pattern_info:
            return False

        # 检查长度
        if len(token) < pattern_info.get('min_length', 0):
            return False

        if len(token) > pattern_info.get('max_length', float('inf')):
            return False

        # 检查正则匹配
        regex = pattern_info['regex']
        return bool(re.match(regex, token))
