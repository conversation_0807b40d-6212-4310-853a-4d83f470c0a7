#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token提取器模块
"""

import asyncio
import aiohttp
import re
import hashlib
from typing import List, Set, Optional
from datetime import datetime

from core.models import TokenInfo, SearchResult
from extraction.patterns import TokenPatterns
from utils.logger import SecurityLogger
from config.settings import settings


class TokenExtractor:
    """Token提取器"""

    def __init__(self, logger: SecurityLogger):
        self.logger = logger
        self.patterns = TokenPatterns()
        self.extracted_tokens: Set[str] = set()
        self.timeout = aiohttp.ClientTimeout(total=30)

    async def extract_from_search_results(self, search_results: List[SearchResult]) -> List[TokenInfo]:
        """从搜索结果中提取tokens"""
        all_tokens = []
        processed_files = set()

        self.logger.info(f"🔍 开始从 {len(search_results)} 个文件中提取tokens...")

        # 创建异步任务
        semaphore = asyncio.Semaphore(10)  # 限制并发数

        async def extract_with_semaphore(result):
            async with semaphore:
                return await self._extract_from_single_result(result, processed_files)

        tasks = [extract_with_semaphore(result) for result in search_results]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"❌ 提取过程异常: {str(result)}")
                continue
            if result:
                all_tokens.extend(result)

        # 去重处理
        unique_tokens = self._deduplicate_tokens(all_tokens)

        self.logger.info(f"✅ 提取完成，发现 {len(all_tokens)} 个token，去重后 {len(unique_tokens)} 个")
        return unique_tokens

    async def _extract_from_single_result(self, result: SearchResult, processed_files: Set[str]) -> List[TokenInfo]:
        """从单个搜索结果提取tokens"""
        tokens = []

        # 生成文件唯一标识
        file_id = f"{result.repository}/{result.file_path}"

        # 避免重复处理
        if file_id in processed_files:
            return tokens
        processed_files.add(file_id)

        try:
            if not result.download_url:
                return tokens

            # 下载文件内容
            content = await self._download_file_content(result.download_url)
            if not content:
                return tokens

            # 提取tokens
            tokens = self._extract_tokens_from_content(content, result)

            if tokens:
                self.logger.debug(f"🎯 从 {file_id} 发现 {len(tokens)} 个token")

        except Exception as e:
            self.logger.error(f"❌ 处理文件失败 {file_id}: {str(e)}")

        return tokens

    async def _download_file_content(self, download_url: str) -> Optional[str]:
        """下载文件内容"""
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.get(download_url) as response:
                    if response.status == 200:
                        content = await response.text(encoding='utf-8', errors='ignore')

                        # 限制文件大小（避免处理过大文件）
                        if len(content) > 500000:  # 500KB
                            self.logger.warning(f"⚠️ 文件过大，跳过: {download_url}")
                            return None

                        return content
                    else:
                        self.logger.warning(f"⚠️ 下载失败 {response.status}: {download_url}")
                        return None

        except Exception as e:
            self.logger.error(f"❌ 下载文件异常 {download_url}: {str(e)}")
            return None

    def _extract_tokens_from_content(self, content: str, result: SearchResult) -> List[TokenInfo]:
        """从文件内容中提取tokens"""
        tokens = []

        # 获取所有token模式
        all_patterns = self.patterns.get_all_patterns()

        for pattern_name, pattern_info in all_patterns.items():
            try:
                matches = re.finditer(pattern_info['regex'], content, re.IGNORECASE | re.MULTILINE)

                for match in matches:
                    token_value = match.group(1) if match.groups() else match.group(0)

                    # 基本验证
                    if not self._is_valid_token_format(token_value, pattern_info):
                        continue

                    # 避免重复
                    if token_value in self.extracted_tokens:
                        continue

                    self.extracted_tokens.add(token_value)

                    # 提取上下文
                    context = self._extract_context(content, match.start(), match.end())

                    token_info = TokenInfo(
                        token=token_value,
                        token_type=pattern_info['type'],
                        source_url=result.file_url,
                        source_file=f"{result.repository}/{result.file_path}",
                        context=context,
                        discovered_at=datetime.now()
                    )

                    tokens.append(token_info)

            except Exception as e:
                self.logger.error(f"❌ 正则匹配失败 {pattern_name}: {str(e)}")

        return tokens

    def _is_valid_token_format(self, token: str, pattern_info: dict) -> bool:
        """验证token格式是否有效"""
        # 长度检查
        if len(token) < pattern_info.get('min_length', 10):
            return False

        if len(token) > pattern_info.get('max_length', 200):
            return False

        # 排除明显的假token
        invalid_patterns = [
            r'^[0]+$',  # 全零
            r'^[1]+$',  # 全一
            r'^(test|example|demo|placeholder|your_token_here)',  # 测试token
            r'(xxx|yyy|zzz)',  # 占位符
            r'^[a-z]+$',  # 全小写字母
            r'^[A-Z]+$',  # 全大写字母
            r'^[0-9]+$',  # 全数字
            r'(fake|dummy|sample|template)',  # 假token标识
        ]

        for invalid_pattern in invalid_patterns:
            if re.search(invalid_pattern, token, re.IGNORECASE):
                return False

        # 检查字符多样性（防止简单重复）
        unique_chars = len(set(token.lower()))
        if unique_chars < min(len(token) // 4, 8):  # 字符种类过少
            return False

        return True

    def _extract_context(self, content: str, start: int, end: int, context_size: int = 150) -> str:
        """提取token周围的上下文"""
        lines = content.split('\n')

        # 找到token所在行
        current_pos = 0
        token_line = 0

        for i, line in enumerate(lines):
            if current_pos <= start < current_pos + len(line):
                token_line = i
                break
            current_pos += len(line) + 1  # +1 for newline

        # 提取上下文行（前后各2行）
        start_line = max(0, token_line - 2)
        end_line = min(len(lines), token_line + 3)

        context_lines = lines[start_line:end_line]

        # 标记包含token的行
        if token_line - start_line < len(context_lines):
            original_line = context_lines[token_line - start_line]
            # 用***标记token位置但不显示实际token值
            masked_line = original_line.replace(
                content[start:end],
                '[***MASKED_TOKEN***]'
            )
            context_lines[token_line - start_line] = f">>> {masked_line}"

        context = '\n'.join(context_lines)

        # 限制上下文长度
        if len(context) > context_size:
            context = context[:context_size] + "..."

        return context

    def _deduplicate_tokens(self, tokens: List[TokenInfo]) -> List[TokenInfo]:
        """去重tokens"""
        seen_tokens = set()
        unique_tokens = []

        for token in tokens:
            # 使用token值的hash作为唯一标识
            token_hash = hashlib.sha256(token.token.encode()).hexdigest()

            if token_hash not in seen_tokens:
                seen_tokens.add(token_hash)
                unique_tokens.append(token)

        return unique_tokens

    def _classify_token_sensitivity(self, token: TokenInfo) -> str:
        """分类token敏感度"""
        context_lower = token.context.lower()
        file_lower = token.source_file.lower()

        # 高敏感度指标
        high_sensitivity_indicators = [
            'production', 'prod', 'live', 'main', 'master',
            'deploy', 'release', 'ci', 'cd', 'pipeline'
        ]

        # 中等敏感度指标
        medium_sensitivity_indicators = [
            'dev', 'development', 'test', 'staging', 'config'
        ]

        # 低敏感度指标
        low_sensitivity_indicators = [
            'example', 'demo', 'sample', 'template', 'tutorial'
        ]

        # 检查高敏感度
        if any(indicator in context_lower or indicator in file_lower
               for indicator in high_sensitivity_indicators):
            return 'high'

        # 检查中等敏感度
        if any(indicator in context_lower or indicator in file_lower
               for indicator in medium_sensitivity_indicators):
            return 'medium'

        # 检查低敏感度
        if any(indicator in context_lower or indicator in file_lower
               for indicator in low_sensitivity_indicators):
            return 'low'

        return 'unknown'

    def get_extraction_stats(self) -> dict:
        """获取提取统计信息"""
        return {
            'total_extracted': len(self.extracted_tokens),
            'unique_tokens': len(self.extracted_tokens)
        }

    def clear_cache(self):
        """清空提取缓存"""
        self.extracted_tokens.clear()
        self.logger.info("🧹 Token提取器缓存已清空")

    async def extract_from_text(self, text: str, source: str = "manual_input") -> List[TokenInfo]:
        """从文本中直接提取tokens（用于单独的文本分析）"""
        # 创建临时搜索结果对象
        temp_result = SearchResult(
            query_type="manual",
            priority="high",
            file_url="",
            download_url="",
            repository="manual",
            file_path=source,
            file_name=source,
            sha="",
            size=len(text)
        )

        return self._extract_tokens_from_content(text, temp_result)

    def validate_extracted_token(self, token: str, token_type: str) -> bool:
        """验证单个提取的token是否符合预期格式"""
        pattern_info = self.patterns.get_pattern_by_type(token_type)

        if not pattern_info:
            return False

        return bool(re.match(pattern_info['regex'], token)) and \
            self._is_valid_token_format(token, pattern_info)

